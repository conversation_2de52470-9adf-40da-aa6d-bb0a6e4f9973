import { api<PERSON>lient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

/**
 * Get authentication headers for API requests
 * CORS FIX: X-Partner-ID header removed as it's not allowed by server CORS policy
 */
const getAuthHeaders = () => {
  const token = localStorage.getItem('token')
  const selectedClientId = localStorage.getItem('selectedClientId')

  const headers: Record<string, string> = {}

  if (token) {
    headers['X-Access'] = token
  }

  if (selectedClientId) {
    headers['X-Client-ID'] = selectedClientId
  }

  // X-Partner-ID header removed - partner filtering handled via partner_ids parameter in payload

  return headers
}

// System-related interfaces
export interface SystemUser {
  user_id: string
  username: string
  email_address: string
  msisdn: string
  role_id: number
  role_name: string
  permissions: Permission[]
  permissions_list: Permission[]
  status: number
  created_at: string
  last_login?: string
  client_id?: string
  client_name?: string
  // Enhanced fields to match database schema
  display_name?: string
  user_name?: string
  timestamp?: string
  permissions_acl?: string
  user_type?: 'Admin' | 'Partner'
  partners?: number[] // Array of partner IDs for this user
  updated_at?: string
}

export interface Role {
  role_id: number
  role_name: string
  description?: string
  permissions: Permission[]
  status: number
  created_at: string
  updated_at?: string
  users_count?: number
  // Enhanced fields for role templates
  permissions_acl?: string // Comma or colon separated permission IDs
  id?: string // Alternative ID field from API
  name?: string // Alternative name field from API
}

export interface Permission {
  id: number
  name: string
  description?: string
  module?: string
  status: number
  created_at?: string
}

export interface CreateUserPayload {
  username: string
  email_address: string
  msisdn: string
  role_id: number
  permissions: number[]
  client_id?: string
  password?: string
  // Enhanced fields
  display_name?: string
  user_name?: string
  user_type?: 'Admin' | 'Partner'
  permissions_acl?: string
  partners?: number[] // Array of partner IDs to assign
  first_name?: string
  middle_name?: string
  last_name?: string
  national_id?: string
  nationality?: string
  identifier_type?: string
}

export interface UpdateUserPayload {
  user_id: string
  username?: string
  email_address?: string
  msisdn?: string
  role_id?: number
  permissions?: number[]
  status?: number
  // Enhanced fields
  display_name?: string
  user_name?: string
  user_type?: 'Admin' | 'Partner'
  permissions_acl?: string
  partners?: number[] // Array of partner IDs to assign
}

export interface CreateRolePayload {
  role_name: string
  description?: string
  permissions: number[]
}

export interface UpdateRolePayload {
  role_id: number
  role_name?: string
  description?: string
  permissions?: number[]
  status?: number
}

export interface CreatePermissionPayload {
  name: string
  description?: string
  module?: string
  status?: number
}

export interface UpdatePermissionPayload {
  id: number
  name: string
  description?: string
  module?: string
  status?: number
}

export interface UserFilters {
  role_id?: number
  status?: number
  client_id?: string
  search?: string
}

export interface RoleFilters {
  status?: number
  search?: string
}

/**
 * System API service for users, roles, and permissions
 */
export const systemApi = {
  /**
   * Get system users with pagination and filters
   */
  async getUsers(
    params: PaginationParams & UserFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<SystemUser>>> {
    try {
      const payload = {
        page: params.page || 1,
        per_page: params.limit || 10,
        role_id: params.role_id || '',
        status: params.status || '',
        client_id: params.client_id || '',
        search: params.search || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      // Convert to query string for GET request
      const queryString = new URLSearchParams(
        Object.entries(payload).filter(([_, value]) => value !== '').map(([key, value]) => [key, String(value)])
      ).toString()
      
        const response = await apiClient.get(`users/v1/list?${queryString}`, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })
      
      // Handle response structure from API - check for new error format first
      if (response.data.code === 'Error') {
        const errorData = response.data.data
        return {
          status: errorData.code || 500,
          message: {
            data: [],
            total: 0,
            total_count: 0,
            current_page: 1,
            per_page: payload.per_page
          },
          code: (errorData.code || 500).toString()
        }
      }

      // Handle successful response - try multiple data extraction patterns
      const responseData = response.data.data || response.data
      let userData = []

      // Try different data extraction patterns based on API response structure
      if (responseData.data && responseData.data.data) {
        userData = responseData.data.data // response.data.data.data.data
      } else if (responseData.data && responseData.data.result) {
        userData = responseData.data.result // response.data.data.data.result
      } else if (responseData.data) {
        userData = responseData.data // response.data.data.data
      } else if (responseData.result) {
        userData = responseData.result // response.data.data.result
      } else if (Array.isArray(responseData)) {
        userData = responseData // response.data.data (direct array)
      }

      return {
        status: responseData.code === 200 ? 200 : responseData.code || 200,
        message: {
          data: userData || [],
          total: responseData.total || responseData.total_count || 0,
          total_count: responseData.total_count || responseData.total || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.per_page
        },
        code: (responseData.code || 200).toString()
      }
    } catch (error: any) {
      console.error('Error fetching users:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],              
          total:  0,
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create new system user
   */
  async createUser(userData: CreateUserPayload): Promise<ApiResponse> {
    try {
      // Validate user data
      const validation = this.validateUserData(userData)
      if (!validation.valid) {
        return {
          status: 400,
          message: validation.errors.join(', '),
          code: '400'
        }
      }
      const payload = {
        username: userData.username,
        email_address: userData.email_address,
        msisdn: userData.msisdn,
        role_id: userData.role_id,
        permissions: userData.permissions,
        client_id: userData.client_id || '',
        password: userData.password ? btoa(userData.password) : '',
        timestamp: getCurrentTimestamp(),
        // Enhanced fields
        display_name: userData.display_name || '',
        user_name: userData.user_name || userData.username,
        user_type: userData.user_type || 'Partner',
        permissions_acl: userData.permissions_acl || '',
        partners: userData.partners || [],
        first_name: userData.first_name || '',
        middle_name: userData.middle_name || '',
        last_name: userData.last_name || '',
        national_id: userData.national_id || '',
        nationality: userData.nationality || '',
        identifier_type: userData.identifier_type || ''
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('users/v1/account_create', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to create user',
        code: '500'
      }
    }
  },

  /**
   * Update system user
   */
  async updateUser(userData: UpdateUserPayload): Promise<ApiResponse> {
    try {
      // Validate user data
      const validation = this.validateUserData(userData)
      if (!validation.valid) {
        return {
          status: 400,
          message: validation.errors.join(', '),
          code: '400'
        }
      }
      const payload = {
        user_id: userData.user_id,
        username: userData.username,
        email_address: userData.email_address,
        msisdn: userData.msisdn,
        role_id: userData.role_id,
        permissions: userData.permissions,
        status: userData.status,
        timestamp: getCurrentTimestamp(),
        // Enhanced fields
        display_name: userData.display_name || '',
        user_name: userData.user_name || userData.username,
        user_type: userData.user_type || 'Partner',
        permissions_acl: userData.permissions_acl || '',
        partners: userData.partners || []
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post(`users/v1/edit/${userData.user_id}`, payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to update user',
        code: '500'
      }
    }
  },

  /**
   * Delete system user
   */
  async deleteUser(userId: string): Promise<ApiResponse> {
    try {
      const payload = { user_id: userId }
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/user_delete', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to delete user',
        code: '500'
      }
    }
  },

  /**
   * Get system roles with pagination and filters
   */
  async getRoles(
    params: PaginationParams & RoleFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<Role>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        status: params.status || '',
        search: params.search || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp(),
        ...params
      }
      
      const hash = createHashKey(payload)

      // Convert to query string for GET request
      const queryString = new URLSearchParams(payload as any).toString()
      
      const response = await apiClient.get(`system/v1/roles?${queryString}`, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })
      
      // Handle response structure from API - check for new error format first
      if (response.data.code === 'Error') {
        const errorData = response.data.data
        return {
          status: errorData.code || 500,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: payload.limit || 10
          },
          code: (errorData.code || 500).toString()
        }
      }

      // Handle successful response - try multiple data extraction patterns
      const responseData = response.data.data || response.data
      let rolesData = []

      // Try different data extraction patterns based on API response structure
      if (responseData.data && responseData.data.data) {
        rolesData = responseData.data.data // response.data.data.data.data
      } else if (responseData.data && responseData.data.result) {
        rolesData = responseData.data.result // response.data.data.data.result
      } else if (responseData.data) {
        rolesData = responseData.data // response.data.data.data
      } else if (responseData.result) {
        rolesData = responseData.result // response.data.data.result
      } else if (Array.isArray(responseData)) {
        rolesData = responseData // response.data.data (direct array)
      }

      // Map the API response to match expected interface
      const mappedRoles = (rolesData || []).map((role: any) => ({
        id: parseInt(role.id),
        name: role.name,
        description: role.description,
        permissions: role.permissions_acl || [],
        permissions_acl: role.permissions_acl || '', // Keep original field for role templates
        status: role.status,
        // created_at: new Date().toISOString(), // API doesn't provide this
        // updated_at: new Date().toISOString()  // API doesn't provide this
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code || 200,
        message: {
          data: mappedRoles,
          total: parseInt(responseData.data?.total) || parseInt(responseData.data?.record_count) || parseInt(responseData.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit || 10
        },
        code: (responseData.code || 200).toString()
      }
    } catch (error: any) {
      console.error('Error fetching roles:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },



  /**
   * Assign partners to a user
   */
  async assignPartnersToUser(userId: string, partnerIds: number[]): Promise<ApiResponse> {
    try {
      const payload = {
        user_id: userId,
        partner_ids: partnerIds,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('system/v1/users/assign-partners', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error assigning partners to user:', error)

      return {
        status: 500,
        message: 'Failed to assign partners',
        code: '500'
      }
    }
  },

  /**
   * Get user's assigned partners
   */
  async getUserPartners(userId: string,partner_ids: string): Promise<ApiResponse<any[]>> {
    try {
      const payload = {
        user_id: userId,
        partner_ids: partner_ids,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/view', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || [],
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching user partners:', error)

      return {
        status: 500,
        message: [],
        code: '500'
      }
    }
  },

  /**
   * Parse permissions ACL string to array of permission IDs
   * Supports both comma and colon separated formats
   */
  parsePermissionsAcl(permissionsAcl: string): number[] {
    if (!permissionsAcl) return []

    // Handle both comma and colon separated formats
    const separator = permissionsAcl.includes(':') ? ':' : ','
    return permissionsAcl
      .split(separator)
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id))
  },

  /**
   * Validate user data according to database schema
   */
  validateUserData(userData: CreateUserPayload | UpdateUserPayload): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check required fields for user table
    if ('username' in userData && !userData.username) {
      errors.push('Username is required')
    }

    if ('email_address' in userData && userData.email_address && !this.isValidEmail(userData.email_address)) {
      errors.push('Invalid email address format')
    }

    if ('msisdn' in userData && userData.msisdn && !this.isValidMsisdn(userData.msisdn)) {
      errors.push('Invalid phone number format')
    }

    if ('role_id' in userData && userData.role_id && userData.role_id <= 0) {
      errors.push('Valid role ID is required')
    }

    if ('user_type' in userData && userData.user_type && !['Admin', 'Partner'].includes(userData.user_type)) {
      errors.push('User type must be Admin or Partner')
    }

    // Validate display_name length (database constraint)
    if ('display_name' in userData && userData.display_name && userData.display_name.length > 200) {
      errors.push('Display name must be 200 characters or less')
    }

    // Validate user_name length (database constraint)
    if ('user_name' in userData && userData.user_name && userData.user_name.length > 65) {
      errors.push('Username must be 65 characters or less')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  },

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * Validate MSISDN format
   */
  isValidMsisdn(msisdn: string): boolean {
    // Remove any non-digit characters
    const cleanMsisdn = msisdn.replace(/\D/g, '')
    // Check if it's a valid length (typically 9-15 digits)
    return cleanMsisdn.length >= 9 && cleanMsisdn.length <= 15
  },

  /**
   * Create new role
   */
  async createRole(roleData: CreateRolePayload): Promise<ApiResponse> {
    try {
      const payload = {
        role_name: roleData.role_name,
        description: roleData.description || '',
        permissions: roleData.permissions,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('system/v1/role/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to create role',
        code: '500'
      }
    }
  },

  /**
   * Update role
   */
  async updateRole(roleData: UpdateRolePayload): Promise<ApiResponse> {
    try {
      const payload = {
        role_id: roleData.role_id,
        role_name: roleData.role_name,
        description: roleData.description,
        permissions: roleData.permissions,
        status: roleData.status,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`system/v1/role/update/${roleData.role_id}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to update role',
        code: '500'
      }
    }
  },

  /**
   * Delete role
   */
  async deleteRole(roleId: number): Promise<ApiResponse> {
    try {
      const payload = { role_id: roleId }
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/role/delete', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to delete role',
        code: '500'
      }
    }
  },

  /**
   * Get system permissions
   */
  async getPermissions(
    params: PaginationParams = {}
  ): Promise<ApiResponse<PaginatedResponse<Permission>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 100,
        start: params.start || '',
        end: params.end || '',
        status: params.status || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      // Convert to query string for GET request
      const queryString = new URLSearchParams(payload as any).toString()

      const response = await apiClient.get(`system/v1/permissions?${queryString}`, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle the nested response structure: response.data.data.data.data
      const responseData = response.data.data

      // Map the API response to match expected interface
      // const permissions = responseData.data?.data || []
      // const mappedPermissions = permissions.map((permission: any) => ({
      //   id: parseInt(permission.permission_id || permission.id),
      //   name: permission.permission_name || permission.name,
      //   description: permission.permission_desc || permission.description,
      //   module: permission.module || permission.permission_module,
      //   created_at: permission.created_at || new Date().toISOString(),
      //   updated_at: permission.updated_at || new Date().toISOString()
      // }))
      

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data,
          // data: mappedPermissions,
          total: parseInt(responseData.data?.total) || 0,
          current_page: responseData.data?.current_page || 1,
          limit: params.limit || 100
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching permissions:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 100
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 100
        },
        code: '500'
      }
    }
  },

  /**
   * Assign client to user
   */
  async assignClient(userId: string, clientId: string): Promise<ApiResponse> {
    try {
      const payload = {
        user_id: userId,
        client_id: clientId
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/user_assign_client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error assigning client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to assign client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to assign client',
        code: '500'
      }
    }
  },

  /**
   * Resend OTP to user
   */
  async resendOTP(userId: string): Promise<ApiResponse> {
    try {
      const payload = { user_id: userId }
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/user_resend_otp', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error resending OTP:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to resend OTP',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to resend OTP',
        code: '500'
      }
    }
  },

  /**
   * Create a new permission
   */
  async createPermission(payload: CreatePermissionPayload): Promise<ApiResponse> {
    try {
      const payloadWithTimestamp = {
        ...payload,
        timestamp: getCurrentTimestamp()
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post('system/v1/permission/create', payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating permission:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status,
          message: data.message || 'Failed to create permission',
          code: status.toString()
        }
      } else {
        return {
          status: 500,
          message: 'Network error occurred',
          code: '500'
        }
      }
    }
  },

  /**
   * Update an existing permission
   */
  async updatePermission(payload: UpdatePermissionPayload): Promise<ApiResponse> {
    try {
      const payloadWithTimestamp = {
        ...payload,
        timestamp: getCurrentTimestamp()
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post(`system/v1/permission/update/${payload.id}`, payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating permission:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status,
          message: data.message || 'Failed to update permission',
          code: status.toString()
        }
      } else {
        return {
          status: 500,
          message: 'Network error occurred',
          code: '500'
        }
      }
    }
  },

  /**
   * Delete a permission
   */
  async deletePermission(permissionId: number): Promise<ApiResponse> {
    try {
      const payload = { id: permissionId }
      const hash = createHashKey(payload)

      const response = await apiClient.delete(`merchant/v1/permissions/${permissionId}`, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting permission:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status,
          message: data.message || 'Failed to delete permission',
          code: status.toString()
        }
      } else {
        return {
          status: 500,
          message: 'Network error occurred',
          code: '500'
        }
      }
    }
  },

  /**
   * Reset user password
   */
  async resetUserPassword(payload: { username: string; new_password: string }): Promise<ApiResponse> {
    try {
      const payloadWithTimestamp = {
        ...payload,
        new_password: btoa(payload.new_password),
        timestamp: getCurrentTimestamp()
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post('users/v1/password_reset', payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || 'Password reset successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Reset password error:', error)
      return {
        status: error.response?.status || 500,
        message: error.response?.data?.message || 'Failed to reset password',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Change user password
   */
  async changeUserPassword(payload: { username: string; old_password: string; new_password: string }): Promise<ApiResponse> {
    try {
      const payloadWithTimestamp = {
        ...payload,
        old_password: btoa(payload.old_password),
        new_password: btoa(payload.new_password),
        timestamp: getCurrentTimestamp()
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post('users/v1/password_change', payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || 'Password changed successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Change password error:', error)
      return {
        status: error.response?.status || 500,
        message: error.response?.data?.message || 'Failed to change password',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Blacklist/unblacklist user
   */
  async blacklistUser(userId: number, blacklist: boolean = true): Promise<ApiResponse> {
    try {
      const payloadWithTimestamp = {
        user_id: userId,
        blacklist,
        timestamp: getCurrentTimestamp()
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post(`system/v1/users/blacklist/${userId}`, payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || 'User status updated successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Blacklist user error:', error)
      return {
        status: error.response?.status || 500,
        message: error.response?.data?.message || 'Failed to update user status',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Get system users with pagination and filters
   */
  async getSystemUsers(params: PaginationParams & { role_id?: number; status?: number } = {}): Promise<ApiResponse<any>> {
    try {
      const payloadWithTimestamp = {
        page: params.page || 1,
        limit: params.limit || 10,
        role_id: params.role_id || '',
        status: params.status || '',
        search: params.search || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp(),
        ...params
      }
      const hash = createHashKey(payloadWithTimestamp)

      const response = await apiClient.post('system/v1/users', payloadWithTimestamp, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const users = responseData.data?.data || []
      const mappedUsers = users.map((user: any) => ({
        id: parseInt(user.user_id || user.id),
        username: user.username || user.user_name,
        email: user.email_address || user.email,
        role_id: parseInt(user.role_id),
        role_name: user.role_name,
        partner_id: user.partner_id,
        partner_name: user.partner_name,
        status: parseInt(user.status),
        last_login: user.last_logged_on || user.last_login,
        created_at: user.created_at || new Date().toISOString(),
        updated_at: user.updated_at || new Date().toISOString()
      }))

      return {
        status: responseData.code,
        message: {
          data: mappedUsers,
          total: parseInt(responseData.data?.total) || 0,
          current_page: responseData.data?.current_page || 1,
          limit: responseData.data?.per_page || payloadWithTimestamp.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get system users error:', error)
      return {
        status: error.response?.status || 500,
        message: error.response?.data?.message || 'Failed to fetch users',
        code: (error.response?.status || 500).toString()
      }
    }
  }
}
