<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
  

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="partner-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Partner</label>
          <select id="partner-filter" v-model="filters.partner_id" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Partners</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
          <select id="status-filter" v-model="filters.status" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Partner Settings Table -->
    <DataTable :data="partnerSettings" :headers="settingsHeaders" :loading="loading" :current-page="currentPage" :total-records="totalRecords"
      :exclude-columns="['id','partner_id']"
      :page-size="pageSize" title="Partner Settings" row-key="id" :has-actions="true" @page-change="handlePageChange"
      @search="handleSearch" @sort="handleSort" @row-click="handleRowClick">
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button @click="refreshData" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button @click="showAddModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Setting
          </button>
        </div>
      </template>

      <!-- Custom Rate Limit Cell -->
      <template #cell-rate_limit_per_minute="{ value }">
        <span class=" dark:text-gray-300">
          {{ value ?? '-' }}
        </span>
      </template>


      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span :class="{
          'bg-green-100 text-green-800': value === '1' || value === 'active',
          'bg-yellow-100 text-yellow-800': value === '0' || value === 'inactive',
          'bg-red-100 text-red-800': value === '3' || value === 'suspended',
        }" class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium capitalize">
          {{
            value === '0' || value === 'inactive'
              ? 'Inactive'
              : value === '1' || value === 'active'
                ? 'Active'
                : value === '3' || value === 'suspended'
                  ? 'Suspended'
                  : value === '2'
                    ? 'Lost'
          : '-'
          }}
        </span>
      </template>


      <!-- Custom Service Type Cell -->
      <template #cell-service_type="{ value }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {{ value ? value.charAt(0).toUpperCase() + value.slice(1) : '-' }}
        </span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <div class="flex items-center space-x-2">
          <button @click="viewService(item)"
            class="text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
            View
          </button>
          <button @click="editService(item)"
            class="text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200">
            Edit
          </button>
          <button @click="deleteService(item)"
            class="text-red-600 hover:text-red-900 text-sm font-medium transition-colors duration-200">
            Delete
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { partnerApi } from '@/services/partnerApi'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const partnerSettings = ref<any[]>([])
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)


// Table headers
const settingsHeaders = computed(() => ({
  // setting_id: 'Setting ID',
  partner_name: 'Partner Name',
  api_key: 'API Key',
  currency: 'Currency',
  denomination: 'Denomination',
  billing_mode: 'Billing Mode',
  rate_limit: 'Rate Limit',
  timezone: 'Timezone',
  ip_address: 'IP Address',
  callback_url: 'Callback URL',
  websites: 'Websites',
  version: 'Version',
  status: 'Status',
  created_at: 'Created',
  updated_at: 'Updated'
}))
// Filters
const filters = reactive({
  status: '',
  service_type: '',
  partner_id: ''
})


// 


// Methods
const loadData = async () => {
  loading.value = true
  try {
    // Load Partner Settings from API
    const response = await partnerApi.getPartnerSettings({
      page: currentPage.value,
      limit: pageSize.value,
      partner_ids: filters.partner_id || route.query.partner_id as string || '',
      status: filters.status,
      search: searchQuery.value
    })

    // console.log('Partner Settings response:', response)

    if (response.status === 200) {
      partnerSettings.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load Partner Settings:', response)
      partnerSettings.value = []
      totalRecords.value = 0
    }

    // Load partners for filter dropdown
    const partnersResponse = await partnerApi.getPartners({ limit: 100 })
    if (partnersResponse.status === 200) {
      partners.value = partnersResponse.message.data || []
    }
  } catch (error) {
    console.error('Error loading Partner Settings:', error)
    partnerSettings.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewService(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewService = (service: any) => {
  console.log('View service:', service)
  // Implement view logic
}

const editService = (service: any) => {
  console.log('Edit service:', service)
  // Implement edit logic
}

const deleteService = (service: any) => {
  console.log('Delete service:', service)
  // Implement delete logic
}

const exportData = () => {
  console.log('Export Partner Settings data')
  // Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
