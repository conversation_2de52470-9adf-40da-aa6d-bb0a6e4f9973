<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add Partner Setting</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Create a new setting for {{ partnerName || 'selected partner' }}
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back
          </button>
        </div>
      </div>
    </div>

    <!-- Setting Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Setting Key -->
        <div>
          <label for="setting_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Setting Key <span class="text-red-500">*</span>
          </label>
          <input
            id="setting_key"
            v-model="settingForm.setting_key"
            type="text"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., max_bet_amount, commission_rate"
          />
        </div>

        <!-- Setting Name -->
        <div>
          <label for="setting_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Setting Name <span class="text-red-500">*</span>
          </label>
          <input
            id="setting_name"
            v-model="settingForm.setting_name"
            type="text"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter setting display name"
          />
        </div>

        <!-- Setting Type -->
        <div>
          <label for="setting_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Setting Type <span class="text-red-500">*</span>
          </label>
          <select
            id="setting_type"
            v-model="settingForm.setting_type"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select setting type</option>
            <option value="string">String</option>
            <option value="number">Number</option>
            <option value="boolean">Boolean</option>
            <option value="json">JSON</option>
            <option value="array">Array</option>
          </select>
        </div>

        <!-- Setting Value -->
        <!-- <div>
          <label for="setting_value" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Setting Value <span class="text-red-500">*</span>
          </label>
          <textarea
            v-if="settingForm.setting_type === 'json' || settingForm.setting_type === 'array'"
            id="setting_value"
            v-model="settingForm.setting_value"
            rows="4"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            :placeholder="settingForm.setting_type === 'json' ? '{\"key\": \"value\"}' : '[\"item1\", \"item2\"]'"
          ></textarea>
          <select
            v-else-if="settingForm.setting_type === 'boolean'"
            id="setting_value"
            v-model="settingForm.setting_value"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select value</option>
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
          <input
            v-else
            id="setting_value"
            v-model="settingForm.setting_value"
            :type="settingForm.setting_type === 'number' ? 'number' : 'text'"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter setting value"
          />
        </div> -->

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <textarea
            id="description"
            v-model="settingForm.description"
            rows="3"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter setting description"
          ></textarea>
        </div>

        <!-- Category -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Category
          </label>
          <select
            id="category"
            v-model="settingForm.category"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select category</option>
            <option value="general">General</option>
            <option value="betting">Betting</option>
            <option value="financial">Financial</option>
            <option value="security">Security</option>
            <option value="integration">Integration</option>
            <option value="notification">Notification</option>
          </select>
        </div>

        <!-- Status -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <select
            id="status"
            v-model="settingForm.status"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Creating...' : 'Create Setting' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { partnerApi } from '@/services/partnerApi'
import { getNavigationData } from '@/utils/navigationCache'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const partnerName = ref('')

// Setting form
const settingForm = ref({
  setting_key: '',
  setting_name: '',
  setting_type: '',
  setting_value: '',
  description: '',
  category: '',
  status: '1',
  partner_id: ''
})

// Methods
const goBack = () => {
  const partnerId = route.query.partner_id as string
  if (partnerId) {
    router.push({ name: 'partner-details', params: { id: partnerId } })
  } else {
    router.push({ name: 'partners-settings' })
  }
}

const submitForm = async () => {
  loading.value = true
  try {
    // Validate JSON/Array values if provided
    if (settingForm.value.setting_type === 'json' || settingForm.value.setting_type === 'array') {
      try {
        JSON.parse(settingForm.value.setting_value)
      } catch (error) {
        alert('Invalid JSON/Array format')
        loading.value = false
        return
      }
    }

    // Set partner ID from route or navigation data
    const partnerId = route.query.partner_id as string
    if (partnerId) {
      settingForm.value.partner_id = partnerId
    }

    // Submit form (API call would go here)
    console.log('Creating setting:', settingForm.value)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Navigate back on success
    goBack()
  } catch (error) {
    console.error('Error creating setting:', error)
    alert('Failed to create setting')
  } finally {
    loading.value = false
  }
}

// Initialize
onMounted(() => {
  // Get partner data from navigation cache or route
  const partnerData = getNavigationData('partner')
  if (partnerData) {
    partnerName.value = partnerData.name || partnerData.partner_name
    settingForm.value.partner_id = partnerData.id || partnerData.partner_id
  } else if (route.query.partner_id) {
    settingForm.value.partner_id = route.query.partner_id as string
  }
})
</script>
