<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add Partner Service</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Create a new service for {{ partnerName || 'selected partner' }}
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back
          </button>
        </div>
      </div>
    </div>

    <!-- Service Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Service Name -->
        <div>
          <label for="service_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Service Name <span class="text-red-500">*</span>
          </label>
          <input
            id="service_name"
            v-model="serviceForm.service_name"
            type="text"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter service name"
          />
        </div>

        <!-- Service Type -->
        <div>
          <label for="service_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Service Type <span class="text-red-500">*</span>
          </label>
          <select
            id="service_type"
            v-model="serviceForm.service_type"
            required
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select service type</option>
            <option value="api">API Service</option>
            <option value="webhook">Webhook Service</option>
            <option value="integration">Integration Service</option>
            <option value="custom">Custom Service</option>
          </select>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <textarea
            id="description"
            v-model="serviceForm.description"
            rows="3"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter service description"
          ></textarea>
        </div>

        <!-- Configuration -->
        <div>
          <label for="configuration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Configuration (JSON)
          </label>
          <textarea
            id="configuration"
            v-model="serviceForm.configuration"
            rows="5"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            placeholder='{"key": "value"}'
          ></textarea>
        </div>

        <!-- Status -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <select
            id="status"
            v-model="serviceForm.status"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Creating...' : 'Create Service' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { partnerApi } from '@/services/partnerApi'
import { getNavigationData } from '@/utils/navigationCache'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const partnerName = ref('')

// Service form
const serviceForm = ref({
  service_name: '',
  service_type: '',
  description: '',
  configuration: '',
  status: '1',
  partner_id: ''
})

// Methods
const goBack = () => {
  const partnerId = route.query.partner_id as string
  if (partnerId) {
    router.push({ name: 'partner-details', params: { id: partnerId } })
  } else {
    router.push({ name: 'partner-services' })
  }
}

const submitForm = async () => {
  loading.value = true
  try {
    // Validate JSON configuration if provided
    if (serviceForm.value.configuration) {
      try {
        JSON.parse(serviceForm.value.configuration)
      } catch (error) {
        alert('Invalid JSON configuration')
        loading.value = false
        return
      }
    }

    // Set partner ID from route or navigation data
    const partnerId = route.query.partner_id as string
    if (partnerId) {
      serviceForm.value.partner_id = partnerId
    }

    // Submit form (API call would go here)
    console.log('Creating service:', serviceForm.value)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Navigate back on success
    goBack()
  } catch (error) {
    console.error('Error creating service:', error)
    alert('Failed to create service')
  } finally {
    loading.value = false
  }
}

// Initialize
onMounted(() => {
  // Get partner data from navigation cache or route
  const partnerData = getNavigationData('partner')
  if (partnerData) {
    partnerName.value = partnerData.name || partnerData.partner_name
    serviceForm.value.partner_id = partnerData.id || partnerData.partner_id
  } else if (route.query.partner_id) {
    serviceForm.value.partner_id = route.query.partner_id as string
  }
})
</script>
