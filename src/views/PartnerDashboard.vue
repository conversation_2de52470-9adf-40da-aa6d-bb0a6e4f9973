<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome back{{ userName ? `, ${userName}` : '' }}!</h2>
          <p class="text-gray-600 dark:text-gray-300 mt-1">Here's your partner performance overview.</p>
        </div>
        <div class="hidden md:block">
          <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div class="text-green-600 dark:text-green-400 text-sm font-medium">Partner Status</div>
            <div class="text-green-900 dark:text-green-300 text-lg font-semibold">Active</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Partner Stats Grid -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="i in 4" :key="i" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-gray-200 dark:bg-gray-700 w-12 h-12"></div>
          <div class="ml-4 flex-1">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Bets -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Bets</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatNumberPrivacy(partnerStats.total_bets, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 dark:text-green-400 text-sm font-medium">{{ formatNumberPrivacy(partnerStats.active_bets, authStore.isPrivateMode) }} active</span>
        </div>
      </div>

      <!-- Total Revenue -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Revenue</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">KES {{ formatCurrencyPrivacy(partnerStats.total_revenue, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 dark:text-green-400 text-sm font-medium">+{{ formatNumberPrivacy(partnerStats.revenue_growth, authStore.isPrivateMode, 1) }}% this month</span>
        </div>
      </div>

      <!-- Win Rate -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Win Rate</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatNumberPrivacy(partnerStats.win_rate, authStore.isPrivateMode, 1) }}%</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-blue-600 dark:text-blue-400 text-sm font-medium">{{ formatNumberPrivacy(partnerStats.total_wins, authStore.isPrivateMode) }} wins</span>
        </div>
      </div>

      <!-- Active Players -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-orange-100 dark:bg-orange-900/20">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Active Players</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatNumberPrivacy(partnerStats.active_players, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 dark:text-green-400 text-sm font-medium">{{ partnerStats.new_players }} new today</span>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <router-link
          :to="{ name: 'partners-bets' }"
          class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <div class="p-2 bg-blue-100 dark:bg-blue-900/40 rounded-lg">
            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">View Bets</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manage betting activity</p>
          </div>
        </router-link>

        <router-link
          :to="{ name: 'partners-bet-slips' }"
          class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <div class="p-2 bg-green-100 dark:bg-green-900/40 rounded-lg">
            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">Bet Slips</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Review bet slips</p>
          </div>
        </router-link>

        <router-link
          :to="{ name: 'partner-services' }"
          class="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
        >
          <div class="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-lg">
            <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">Services</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manage services</p>
          </div>
        </router-link>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Bets -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Bets</h3>
        <div class="space-y-3">
          <div v-for="bet in recentBets" :key="bet.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ bet.description }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ bet.time }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm font-semibold" :class="bet.status === 'won' ? 'text-green-600 dark:text-green-400' : bet.status === 'lost' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'">
                KES {{ formatCurrency(bet.amount) }}
              </p>
              <p class="text-xs capitalize" :class="bet.status === 'won' ? 'text-green-600 dark:text-green-400' : bet.status === 'lost' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'">
                {{ bet.status }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Chart Placeholder -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Overview</h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="text-center">
            <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            <p class="text-gray-500 dark:text-gray-400">Chart will be added here</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { getPrivacyIcon, getPrivacyTooltip, formatCurrencyPrivacy, formatNumberPrivacy } from '@/utils/privacy'

// Stores
const authStore = useAuthStore()

// Reactive data
const loading = ref(true)
const partnerStats = ref({
  total_bets: 1250,
  active_bets: 89,
  total_revenue: 125000,
  revenue_growth: 12.5,
  win_rate: 68.5,
  total_wins: 856,
  active_players: 342,
  new_players: 15
})

// User name from auth store
const userName = computed(() => {
  return authStore.user?.display_name || authStore.user?.username || null
})

// Methods
const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num)
}

const formatCurrency = (num: number): string => {
  return new Intl.NumberFormat('en-KE', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num)
}

const loadPartnerStats = async () => {
  loading.value = true
  try {
    // TODO: Replace with actual API call
    // const response = await partnerApi.getStats({
    //   partner_id: authStore.selectedPartnerId || ''
    // })
    // if (response.status === 200) {
    //   partnerStats.value = response.message
    // }

    // Simulate API call with partner filtering
    console.log('Loading partner stats for partner:', authStore.selectedPartnerId)
    setTimeout(() => {
      loading.value = false
    }, 1000)
  } catch (error) {
    console.error('Failed to load partner stats:', error)
    loading.value = false
  }
}

// Watch for partner selection changes
watch(() => authStore.selectedPartnerId, () => {
  loadPartnerStats()
}, { immediate: false })

// Lifecycle
onMounted(() => {
  loadPartnerStats()
})

// Sample recent bets
const recentBets = ref([
  { id: 1, description: 'Football Match - Arsenal vs Chelsea', amount: 5000, status: 'won', time: '2 minutes ago' },
  { id: 2, description: 'Basketball - Lakers vs Warriors', amount: 2500, status: 'pending', time: '15 minutes ago' },
  { id: 3, description: 'Tennis - Djokovic vs Nadal', amount: 1200, status: 'lost', time: '1 hour ago' },
  { id: 4, description: 'Football - Manchester United vs Liverpool', amount: 3500, status: 'won', time: '2 hours ago' },
  { id: 5, description: 'Cricket - India vs Australia', amount: 800, status: 'pending', time: '3 hours ago' }
])
</script>
