<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#20ad79]/80 to-[#182232]/80 py-12 px-4 sm:px-6 lg:px-8 bg-gray-900">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto h-20 w-auto flex items-center justify-center">
          <img src="@/assets/moss_logo_full.png" alt="Mossbets B2B Logo" class="h-20 w-auto" />
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-300">
          Reset Password
        </h2>
        <p class="mt-2 text-sm text-gray-300">
          Enter the verification code and your new password
        </p>
      </div>

      <!-- Form -->
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="bg-[#182232] rounded-lg shadow-sm border border-gray-200 p-6">
          <!-- Alert -->
          <div v-if="error" class="mb-4 p-4 rounded-md bg-red-50 border border-red-200">
            <div class="flex">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <div class="ml-3">
                <p class="text-sm text-red-800">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Success Message -->
          <div v-if="successMessage" class="mb-4 p-4 rounded-md bg-green-50 border border-green-200">
            <div class="flex">
              <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <div class="ml-3">
                <p class="text-sm text-green-800">{{ successMessage }}</p>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <!-- Username (readonly) -->
            <div>
              <label for="username" class="block text-sm font-medium text-gray-300">
                Username
              </label>
              <input
                id="username"
                v-model="form.username"
                type="text"
                readonly
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"
              />
            </div>

            <!-- Verification Code -->
            <div>
              <label for="verification_code" class="block text-sm font-medium text-gray-300">
                Verification Code
              </label>
              <input
                id="verification_code"
                v-model="form.verification_code"
                type="text"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter verification code"
              />
            </div>

            <!-- New Password -->
            <div>
              <label for="new_password" class="block text-sm font-medium text-gray-300">
                New Password
              </label>
              <input
                id="new_password"
                v-model="form.new_password"
                type="password"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter new password"
              />
            </div>

            <!-- Confirm Password -->
            <div>
              <label for="confirm_password" class="block text-sm font-medium text-gray-300">
                Confirm Password
              </label>
              <input
                id="confirm_password"
                v-model="form.confirm_password"
                type="password"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Confirm new password"
              />
            </div>
          </div>

          <!-- Submit Button -->
          <div class="mt-6">
            <button
              type="submit"
              :disabled="loading"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              {{ loading ? 'Resetting...' : 'Reset Password' }}
            </button>
          </div>

          <!-- Back to Login Link -->
          <div class="mt-4 text-center">
            <router-link
              :to="{ name: 'login' }"
              class="text-sm text-blue-600 hover:text-blue-500 transition-colors duration-200"
            >
              Back to Sign In
            </router-link>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { authApi } from '@/services/authApi'

// Router and route
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const error = ref('')
const successMessage = ref('')

// Form data
const form = reactive({
  username: '',
  verification_code: '',
  new_password: '',
  confirm_password: '',
  dial_code: '254'
})

// Methods
const handleSubmit = async () => {
  error.value = ''
  successMessage.value = ''

  // Validate passwords match
  if (form.new_password !== form.confirm_password) {
    error.value = 'Passwords do not match'
    return
  }

  // Validate password strength
  if (form.new_password.length < 6) {
    error.value = 'Password must be at least 6 characters long'
    return
  }

  loading.value = true

  try {
    const result = await authApi.resetPassword({
      username: form.username,
      new_password: form.new_password,
      otp_code: form.verification_code,
      dial_code: form.dial_code,
    })
    
    if (result.status === 200) {
      successMessage.value = 'Password reset successfully! Redirecting to login...'
      setTimeout(() => {
        router.push({ name: 'login' })
      }, 2000)
    } else {
      error.value = result.message || 'Failed to reset password'
    }
  } catch (err: any) {
    error.value = err.message || 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}

// Initialize form with username from query params
onMounted(() => {
  if (route.query.username) {
    form.username = route.query.username as string
  }
})
</script>
