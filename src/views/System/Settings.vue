<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage system configuration and global settings
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="refreshData" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
          <button @click="showAddModal = true"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Setting
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- General Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">General Settings</h3>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">System-wide configuration options</p>
        <button @click="activeCategory = 'general'" 
          class="w-full text-left px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-md transition-colors duration-200">
          Manage General Settings
        </button>
      </div>

      <!-- Security Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Security Settings</h3>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Authentication and security configuration</p>
        <button @click="activeCategory = 'security'" 
          class="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 rounded-md transition-colors duration-200">
          Manage Security Settings
        </button>
      </div>

      <!-- API Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API Settings</h3>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">API endpoints and integration settings</p>
        <button @click="activeCategory = 'api'" 
          class="w-full text-left px-4 py-2 text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900 rounded-md transition-colors duration-200">
          Manage API Settings
        </button>
      </div>
    </div>

    <!-- Settings Table -->
    <DataTable 
      :data="filteredSettings" 
      :headers="settingsHeaders" 
      :loading="loading" 
      :current-page="currentPage"
      :total-records="totalRecords" 
      :page-size="pageSize" 
      title="System Settings" 
      row-key="id" 
      :has-actions="true"
      @page-change="handlePageChange" 
      @search="handleSearch" 
      @sort="handleSort" 
      @row-click="handleRowClick">
      
      <!-- Header Actions -->
      <template #header-actions>
        <div class="flex space-x-2">
          <select v-model="filters.category" @change="applyFilters"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Categories</option>
            <option value="general">General</option>
            <option value="security">Security</option>
            <option value="api">API</option>
          </select>
          <button @click="exportSettings" :disabled="loading || settings.length === 0"
            class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export
          </button>
        </div>
      </template>

      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span :class="{
          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': value === 'active',
          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': value === 'inactive'
        }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
          {{ value === 'active' ? 'Active' : 'Inactive' }}
        </span>
      </template>

      <!-- Custom Category Cell -->
      <template #cell-category="{ value }">
        <span :class="{
          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': value === 'general',
          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': value === 'security',
          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': value === 'api'
        }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize">
          {{ value }}
        </span>
      </template>

      <!-- Actions -->
      <template #actions="{ item, closeDropdown }">
        <button @click="editSetting(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit Setting
        </button>
        <button @click="deleteSetting(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
          Delete Setting
        </button>
      </template>
    </DataTable>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { systemApi } from '@/services/systemApi'
import { useSweetAlert } from '@/composables/useSweetAlert'

// Router
const router = useRouter()
const { showSuccess, showError, confirmDelete } = useSweetAlert()

// Reactive data
const loading = ref(false)
const settings = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const activeCategory = ref('')
const showAddModal = ref(false)

// Filters
const filters = reactive({
  category: '',
  status: ''
})

// Table headers
const settingsHeaders = computed(() => ({
  setting_key: 'Setting Key',
  setting_name: 'Name',
  setting_value: 'Value',
  category: 'Category',
  description: 'Description',
  status: 'Status',
  updated_at: 'Last Updated'
}))

// Filtered settings based on active category
const filteredSettings = computed(() => {
  if (!activeCategory.value) return settings.value
  return settings.value.filter(setting => setting.category === activeCategory.value)
})

// Sample data (replace with actual API calls)
const sampleSettings = [
  {
    id: 1,
    setting_key: 'app_name',
    setting_name: 'Application Name',
    setting_value: 'Mossbets B2B Dashboard',
    category: 'general',
    description: 'The name of the application displayed in the header',
    status: 'active',
    updated_at: '2025-08-20 10:30:00'
  },
  {
    id: 2,
    setting_key: 'session_timeout',
    setting_name: 'Session Timeout',
    setting_value: '3600',
    category: 'security',
    description: 'Session timeout in seconds',
    status: 'active',
    updated_at: '2025-08-20 09:15:00'
  },
  {
    id: 3,
    setting_key: 'api_rate_limit',
    setting_name: 'API Rate Limit',
    setting_value: '1000',
    category: 'api',
    description: 'Maximum API requests per hour',
    status: 'active',
    updated_at: '2025-08-20 08:45:00'
  }
]

// Methods
const loadData = async () => {
  loading.value = true
  try {
    // TODO: Replace with actual API call
    // const response = await systemApi.getSettings({
    //   page: currentPage.value,
    //   limit: pageSize.value,
    //   category: filters.category,
    //   status: filters.status,
    //   search: searchQuery.value
    // })
    
    // For now, use sample data
    settings.value = sampleSettings
    totalRecords.value = sampleSettings.length
  } catch (error) {
    console.error('Error loading settings:', error)
    showError('Failed to load settings')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  // Implement sorting logic
  loadData()
}

const handleRowClick = (item: any) => {
  editSetting(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const editSetting = (setting: any) => {
  console.log('Edit setting:', setting)
  // TODO: Implement edit modal
}

const deleteSetting = async (setting: any) => {
  if (await confirmDelete('setting')) {
    try {
      // TODO: Implement delete API call
      // await systemApi.deleteSetting(setting.id)
      showSuccess('Setting deleted successfully!')
      loadData()
    } catch (error) {
      showError('Failed to delete setting')
    }
  }
}

const exportSettings = () => {
  console.log('Export settings')
  // TODO: Implement export functionality
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
