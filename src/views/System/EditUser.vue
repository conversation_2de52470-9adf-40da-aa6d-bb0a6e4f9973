<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Edit User</h1>
          <p class="text-gray-600 mt-1">Update user information, roles and permissions</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Users
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-2 text-sm text-gray-500">Loading user data...</p>
    </div>

    <!-- Form -->
    <div v-else class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="saveUser" class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Username *</label>
              <input
                v-model="userForm.display_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter username"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input
                v-model="userForm.user_name"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter email address"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <input
                v-model="userForm.msisdn"
                type="tel"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter phone number"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="userForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option :value="1">Active</option>
                <option :value="0">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Role Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Role & Permissions</h3>
          <RoleTemplateSelector
            v-model="userForm.role_id"
            @role-selected="onRoleSelected"
            @permissions-changed="onPermissionsChanged"
          />
        </div>

        <!-- Partner Assignment -->
         <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Partner Assignment</h3>

          <!-- Display existing partners if user has partner_ids -->
          <div v-if="userPartners.length > 0" class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Partners</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div
                v-for="partner in userPartners"
                :key="partner.id"
                class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ partner.name || partner.partner_name || `Partner ${partner.id}` }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ID: {{ partner.id }}</p>
                  </div>
                </div>
                <button
                  @click="removePartner(partner.id)"
                  class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  title="Remove partner"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Add new partners section -->
          <div v-if="showPartnerSearch || userPartners.length === 0" class="mb-6">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ userPartners.length === 0 ? 'Assign Partners' : 'Add More Partners' }}
              </h4>
              <button
                v-if="userPartners.length > 0"
                @click="showPartnerSearch = !showPartnerSearch"
                class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                {{ showPartnerSearch ? 'Cancel' : 'Add Partners' }}
              </button>
            </div>

            <PartnerSearchSelector
              v-model="newPartnerIds"
              :user-type="(userForm.user_type as 'Admin' | 'Partner') || 'Partner'"
              :required="userPartners.length === 0"
              :preloaded-partners="allPartners"
              @partners-changed="onNewPartnersChanged"
              @validation-error="onPartnerValidationError"
            />

            <!-- Done button for partner selection -->
            <div v-if="userPartners.length > 0 && showPartnerSearch" class="mt-4 flex justify-end">
              <button
                @click="showPartnerSearch = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Done
              </button>
            </div>
          </div>

          <!-- Show add partners button if user has existing partners but search is hidden -->
          <div v-else-if="userPartners.length > 0 && !showPartnerSearch" class="mb-6">
            <button
              @click="showPartnerSearch = true"
              class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Add Partners
            </button>
          </div>
        </div>

        <!-- Permissions -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-medium text-gray-700">
                {{ userForm.permissions.length }} permissions selected
              </span>
              <div class="flex space-x-2">
                <button
                  type="button"
                  @click="selectAllPermissions"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  Select All
                </button>
                <button
                  type="button"
                  @click="clearAllPermissions"
                  class="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear All
                </button>
              </div>
            </div>
            
            <div class="max-h-60 overflow-y-auto space-y-4">
              <div v-for="(modulePermissions, module) in groupedPermissions" :key="module" class="border border-gray-200 rounded-lg">
                <div class="px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg">
                  <div class="flex items-center">
                    <input
                      :id="`module-${module}`"
                      type="checkbox"
                      :checked="isModuleSelected(modulePermissions)"
                      @change="toggleModule(modulePermissions)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`module-${module}`" class="ml-2 text-sm font-medium text-gray-900 capitalize">
                      {{ formatModuleName(module) }}
                    </label>
                  </div>
                </div>
                <div class="px-3 py-2 space-y-2">
                  <div v-for="permission in modulePermissions" :key="permission.id" class="flex items-center">
                    <input
                      :id="`permission-${permission.id}`"
                      v-model="userForm.permissions"
                      :value="permission.id"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`permission-${permission.id}`" class="ml-2 text-sm text-gray-700">
                      {{ permission.name }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Client Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Partner Assignment</h3>
          <!-- <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Assign Client (Optional)</label>
            <select
              v-model="userForm.client_id"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">No client assigned</option>
              <option v-for="client in clients" :key="client.client_id" :value="client.client_id">
                {{ client.client_name }}
              </option>
            </select>
          </div> -->
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Updating...' : 'Update User' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Role, type Permission, type SystemUser } from '@/services/systemApi'
import { useAuthStore } from '@/stores/auth'
import { useFormData } from '@/composables/useFormData'
import { getNavigationData } from '@/utils/navigationCache'
import RoleTemplateSelector from '@/components/Forms/RoleTemplateSelector.vue'
import PartnerSearchSelector from '@/components/Forms/PartnerSearchSelector.vue'

// Router and stores
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const { prefillForm, storeFormData, clearFormData } = useFormData()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const roles = ref<any[]>([])
const permissions = ref<any[]>([])
const currentUser = ref<any>(null)
const partnerValidationError = ref<string | null>(null)
const selectedRoleTemplate = ref<any | null>(null)
const userPartners = ref<any[]>([])
const partnersLoading = ref(false)
const showPartnerSearch = ref(false)
const newPartnerIds = ref<number[]>([])
const allPartners = ref<any[]>([]) // Store all available partners

// User form
const userForm = ref({
  user_id: '',
  display_name: '',
  user_name: '',
  msisdn: '',
  role_id: '',
  permissions: [] as number[],
  status: 1,
  user_type: 'Partner',
  permissions_acl: '',
  partners: [] as number[],
  partner_ids: '',
  timestamp: ''
})

// Computed properties
const selectedRole = computed(() => {
  return selectedRoleTemplate.value
})

const groupedPermissions = computed(() => {
  const grouped: Record<string, Permission[]> = {}
  
  permissions.value.forEach(permission => {
    const module = permission.module || 'general'
    if (!grouped[module]) {
      grouped[module] = []
    }
    grouped[module].push(permission)
  })
  
  return grouped
})

// Methods
const populateForm = (userData: any) => {
  // Parse permissions from permissions_acl if available
  // Backend uses colon-separated format (2:3:6:8) but converts to comma-separated
  let permissions = []
  if (userData.permissions_acl) {
    // Try colon-separated format first (new format: 2:3:6:8)
    if (userData.permissions_acl.includes(':')) {
      permissions = userData.permissions_acl
        .split(':')
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id))
    } else {
      // Fallback to comma-separated format (legacy: 2,3,6,8)
      permissions = userData.permissions_acl
        .split(',')
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id))
    }
  } else if (userData.permissions) {
    permissions = userData.permissions.map((p: any) => p.id || p)
  }

  const formData = {
    user_id: userData.user_id || userData.id,
    display_name: userData.display_name || '',
    user_name: userData.user_name || userData.username,
    msisdn: userData.msisdn,
    role_id: (userData.role_id || userData.role?.id || '').toString(),
    permissions: permissions,
    status: userData.status || 1,
    user_type: userData.user_type || 'Partner',
    permissions_acl: userData.permissions_acl || '',
    partners: userData.partners || [],
    partner_ids: userData.partner_ids || '',
    timestamp: userData.timestamp || Date.now().toString()
  }

  userForm.value = formData
  console.log('Form populated with user data:', formData)

  // Parse and display partner_info if available
  if (userData.partner_info) {
    const partnerInfoData = parsePartnerInfo(userData.partner_info)
    if (partnerInfoData.length > 0) {
      userPartners.value = partnerInfoData
    }
  }

  // Fetch user's partners if user has partner_ids but no partner_info
  if ((userData.partner_ids || userData.partners) && !userData.partner_info) {
    const userId = userData.user_id || userData.id
    if (userId) {
      fetchUserPartners(userId.toString())
    }
  }
}

const fetchUser = async () => {
  const cachedUser = getNavigationData('user')
  console.log('Cached user found:', !!cachedUser)

  if (cachedUser) {
    currentUser.value = cachedUser
    populateForm(cachedUser)
    console.log('Using cached user data for ID:', cachedUser.user_id || cachedUser.id)
    return
  }

  const userId = route.params.id as string
  if (!userId) {
    router.push({ name: 'system-users' })
    return
  }

  try {
    // This would need a specific API endpoint to get a single user
    // For now, we'll fetch all users and find the one we need
    const response = await systemApi.getUsers({ limit: 1000 })
    if (response.status === 200) {
      const users = response.message?.data || []
      currentUser.value = users.find(user => user.user_id === userId) || null
      
      if (currentUser.value) {
        // Store user data for form prefilling
        storeFormData(currentUser.value, 'user')

        // Populate form with user data
        populateForm(currentUser.value)
      } else {
        router.push({ name: 'system-users' })
      }
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    router.push({ name: 'system-users' })
  } finally {
    initialLoading.value = false
  }
}

const fetchRoles = async () => {
  try {
    const response = await systemApi.getRoles({ limit: 100 })
    if (response.status === 200) {
      roles.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
  }
}

const fetchPermissions = async () => {
  try {
    const response = await systemApi.getPermissions({ limit: 100 })
    if (response.status === 200) {
      permissions.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
  }
}

const fetchClients = async () => {
  // Clients functionality removed as per requirements
}

const onRoleSelected = (role: any | null, permissions: number[], userType: 'Admin' | 'Partner') => {
  selectedRoleTemplate.value = role
  userForm.value.permissions = permissions
  userForm.value.user_type = userType
  // Use colon-separated format as backend expects this format
  userForm.value.permissions_acl = permissions.join(':')

  // Auto-generate display name if not set
  if (!userForm.value.display_name && userForm.value.user_name) {
    userForm.value.display_name = userForm.value.user_name.replace(/_/g, ' ')
  }

}

const onPermissionsChanged = (permissions: number[]) => {
  userForm.value.permissions = permissions
  // Use colon-separated format as backend expects this format
  userForm.value.permissions_acl = permissions.join(':')
}

const onPartnersChanged = (partners: any[]) => {
  // Partners are already updated via v-model
}

const onNewPartnersChanged = (partnerIds: number[]) => {
  newPartnerIds.value = partnerIds
  // Add new partners to the existing list
  partnerIds.forEach(id => {
    if (!userForm.value.partners.includes(id)) {
      userForm.value.partners.push(id)
    }
  })
  // Refresh user partners display when partners are selected
  if (partnerIds.length > 0) {
    const userId = userForm.value.user_id
    if (userId) {
      fetchUserPartners(userId.toString())
    }
  }
}

const removePartner = (partnerId: number) => {
  userPartners.value = userPartners.value.filter(p => p.id !== partnerId)
  // Also remove from form partners array
  userForm.value.partners = userForm.value.partners.filter(id => id !== partnerId)
}

const fetchAllPartners = async () => {
  try {
    const response = await systemApi.getPartners({ limit: 1000 }) // Get all partners
    if (response.status === 200) {
      allPartners.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching all partners:', error)
    allPartners.value = []
  }
}

const fetchUserPartners = async (userId: string) => {
  if (!userId) {
    userPartners.value = []
    return
  }

  partnersLoading.value = true
  try {
    const response = await systemApi.getUserPartners(userId,userForm.value.partner_ids??'')
    if (response.status === 200) {
      userPartners.value = response.message || []
    } else {
      userPartners.value = []
    }
  } catch (error) {
    console.error('Error fetching user partners:', error)
    userPartners.value = []
  } finally {
    partnersLoading.value = false
  }
}

const parsePartnerInfo = (partnerInfoString: string) => {
  try {
    if (!partnerInfoString) return []
    return JSON.parse(partnerInfoString)
  } catch (error) {
    console.error('Error parsing partner_info:', error)
    return []
  }
}

const onPartnerValidationError = (error: string | null) => {
  partnerValidationError.value = error
}

const isModuleSelected = (modulePermissions: Permission[]) => {
  return modulePermissions.every(permission => 
    userForm.value.permissions.includes(permission.id)
  )
}

const toggleModule = (modulePermissions: Permission[]) => {
  const allSelected = isModuleSelected(modulePermissions)
  
  if (allSelected) {
    // Remove all permissions from this module
    modulePermissions.forEach(permission => {
      const index = userForm.value.permissions.indexOf(permission.id)
      if (index > -1) {
        userForm.value.permissions.splice(index, 1)
      }
    })
  } else {
    // Add all permissions from this module
    modulePermissions.forEach(permission => {
      if (!userForm.value.permissions.includes(permission.id)) {
        userForm.value.permissions.push(permission.id)
      }
    })
  }
}

const selectAllPermissions = () => {
  userForm.value.permissions = permissions.value.map(p => p.id)
}

const clearAllPermissions = () => {
  userForm.value.permissions = []
}

const saveUser = async () => {
  loading.value = true
  try {
    // Format permissions_acl as colon-separated string (backend expects this format)
    const permissionsAcl = userForm.value.permissions && userForm.value.permissions.length > 0
      ? userForm.value.permissions.join(':')
      : ''

    const response = await systemApi.updateUser({
      user_id: userForm.value.user_id,
      display_name: userForm.value.display_name,
      user_name: userForm.value.user_name,
      msisdn: userForm.value.msisdn,
      role_id: parseInt(userForm.value.role_id),
      permissions: userForm.value.permissions,
      status: userForm.value.status,
      permissions_acl: permissionsAcl,
      partners: userForm.value.partners
    })
    
    if (response.status === 200) {
      router.push({ name: 'system-users' })
    } else {
      console.error('Failed to update user:', response.message)
    }
  } catch (error) {
    console.error('Error updating user:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-users' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

// Initialize data
onMounted(async () => {
  // First load supporting data
  await Promise.all([
    fetchRoles(),
    fetchPermissions(),
    fetchClients(),
    fetchAllPartners() // Fetch all partners for user assignment
  ])

  // Then try to get user data (cached or from API)
  await fetchUser()

  initialLoading.value = false
})
</script>
