<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Services</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
            Manage and monitor all platform services and integrations
          </p>
        </div>
        
      </div>
    </div>

    <!-- Service Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- create loop of services -->
       <div v-for="value in services" :key="value.id">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">ID: {{value.id}}</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ value.name }}</p>
          </div>
        </div>
      </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { servicesApi } from '@/services/servicesApi'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const services = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)

// Filters
const filters = reactive({
  service_status: '',
  service_category: '',
  environment: '',
  service_version: ''
})

// Remove sample data - using real API calls


// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await servicesApi.getServices({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: filters.service_status,
      start: '',
      end: ''
    })

    if (response.status === 200) {
      services.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load services:', response)
      services.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error loading services:', error)
    services.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewService(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewService = (service: any) => {
  console.log('View service:', service)
  // Implement view logic
}

const editService = (service: any) => {
  console.log('Edit service:', service)
  // Implement edit logic
}

const viewLogs = (service: any) => {
  console.log('View logs for service:', service)
  // Implement logs view logic
}

const exportData = () => {
  console.log('Export services data')
  // Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
