/**
 * Privacy utility functions for masking sensitive data
 */

/**
 * Mask a number with asterisks, showing only the last few digits
 */
export function maskNumber(value: number | string, visibleDigits: number = 2): string {
  if (value === null || value === undefined) return '***'
  
  const str = value.toString()
  if (str.length <= visibleDigits) return '*'.repeat(str.length)
  
  const masked = '*'.repeat(str.length - visibleDigits)
  const visible = str.slice(-visibleDigits)
  return masked + visible
}

/**
 * Mask currency values
 */
export function maskCurrency(value: number | string, currency: string = 'KES', visibleDigits: number = 2): string {
  if (value === null || value === undefined) return `${currency} ***`
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return `${currency} ***`
  
  // Format the number with commas
  const formatted = new Intl.NumberFormat('en-KE', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue)
  
  // Mask all but the last few characters
  const str = formatted.replace(/,/g, '') // Remove commas for masking
  const masked = maskNumber(str, visibleDigits)
  
  return `${currency} ${masked}`
}

/**
 * Mask percentage values
 */
export function maskPercentage(value: number | string, visibleDigits: number = 1): string {
  if (value === null || value === undefined) return '**%'
  
  const str = value.toString()
  const masked = maskNumber(str, visibleDigits)
  return `${masked}%`
}

/**
 * Mask email addresses
 */
export function maskEmail(email: string): string {
  if (!email || !email.includes('@')) return '***@***.***'
  
  const [username, domain] = email.split('@')
  const maskedUsername = username.length > 2 
    ? username[0] + '*'.repeat(username.length - 2) + username[username.length - 1]
    : '*'.repeat(username.length)
  
  const [domainName, extension] = domain.split('.')
  const maskedDomain = domainName.length > 2
    ? domainName[0] + '*'.repeat(domainName.length - 2) + domainName[domainName.length - 1]
    : '*'.repeat(domainName.length)
  
  return `${maskedUsername}@${maskedDomain}.${extension}`
}

/**
 * Mask phone numbers
 */
export function maskPhone(phone: string): string {
  if (!phone) return '***-***-****'
  
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '')
  
  if (digits.length < 4) return '*'.repeat(digits.length)
  
  // Show first 2 and last 2 digits
  const masked = digits.slice(0, 2) + '*'.repeat(digits.length - 4) + digits.slice(-2)
  
  // Format based on length
  if (digits.length === 10) {
    return `${masked.slice(0, 3)}-${masked.slice(3, 6)}-${masked.slice(6)}`
  } else if (digits.length === 12) {
    return `+${masked.slice(0, 3)} ${masked.slice(3, 6)}-${masked.slice(6, 9)}-${masked.slice(9)}`
  }
  
  return masked
}

/**
 * Mask generic text/names
 */
export function maskText(text: string, visibleChars: number = 2): string {
  if (!text) return '***'
  
  if (text.length <= visibleChars) return '*'.repeat(text.length)
  
  return text.slice(0, visibleChars) + '*'.repeat(text.length - visibleChars)
}

/**
 * Mask ID numbers
 */
export function maskId(id: string): string {
  if (!id) return '***'
  
  if (id.length <= 4) return '*'.repeat(id.length)
  
  // Show first 2 and last 2 characters
  return id.slice(0, 2) + '*'.repeat(id.length - 4) + id.slice(-2)
}

/**
 * Conditionally mask data based on privacy mode
 */
export function conditionalMask(
  value: any,
  isPrivate: boolean,
  maskFunction: (value: any) => string,
  ...args: any[]
): string {
  if (!isPrivate) {
    // Return original value formatted appropriately
    if (typeof value === 'number') {
      return new Intl.NumberFormat().format(value)
    }
    return value?.toString() || ''
  }
  
  return maskFunction(value, ...args)
}

/**
 * Privacy-aware currency formatter
 */
export function formatCurrencyPrivacy(
  value: number | string,
  isPrivate: boolean,
  currency: string = 'KES'
): string {
  if (!isPrivate) {
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return `${currency} 0.00`
    
    return `${currency} ${new Intl.NumberFormat('en-KE', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numValue)}`
  }
  
  return maskCurrency(value, currency)
}

/**
 * Privacy-aware number formatter
 */
export function formatNumberPrivacy(
  value: number | string,
  isPrivate: boolean,
  visibleDigits: number = 2
): string {
  if (!isPrivate) {
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return '0'
    
    return new Intl.NumberFormat().format(numValue)
  }
  
  return maskNumber(value, visibleDigits)
}

/**
 * Privacy-aware percentage formatter
 */
export function formatPercentagePrivacy(
  value: number | string,
  isPrivate: boolean,
  visibleDigits: number = 1
): string {
  if (!isPrivate) {
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return '0%'
    
    return `${numValue.toFixed(1)}%`
  }
  
  return maskPercentage(value, visibleDigits)
}

/**
 * Get privacy icon for toggle button
 */
export function getPrivacyIcon(isPrivate: boolean): string {
  return isPrivate ? '👁️‍🗨️' : '👁️'
}

/**
 * Get privacy tooltip text
 */
export function getPrivacyTooltip(isPrivate: boolean): string {
  return isPrivate ? 'Click to show sensitive data' : 'Click to hide sensitive data'
}
