<template>
  <div class="relative">
    <!-- Trigger <PERSON> -->
    <button
      @click="togglePicker"
      type="button"
      class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
      </svg>
      {{ displayText }}
      <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
      </svg>
    </button>

    <!-- Dropdown Panel -->
    <div
      v-if="isOpen"
      class="absolute z-50 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg min-w-80"
      :class="dropdownPosition"
    >
      <div class="p-4">
        <!-- Presets -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Quick Select</h4>
          <div class="grid grid-cols-2 gap-2">
            <button
              v-for="preset in presets"
              :key="preset.label"
              @click="selectPreset(preset)"
              class="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md transition-colors"
            >
              {{ preset.label }}
            </button>
          </div>
        </div>

        <!-- Custom Range Inputs -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Custom Range</h4>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Start Date</label>
              <input
                v-model="localStartDate"
                @change="onDateChange"
                type="date"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">End Date</label>
              <input
                v-model="localEndDate"
                @change="onDateChange"
                type="date"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <!-- Custom Month/Year Selectors -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Last N Months/Years</h4>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Last N Months (2-12)</label>
              <div class="flex">
                <input
                  v-model.number="customMonths"
                  type="number"
                  min="2"
                  max="12"
                  class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  @click="selectCustomMonths"
                  class="px-3 py-2 text-sm bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Apply
                </button>
              </div>
            </div>
            <div>
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Last N Years</label>
              <div class="flex">
                <input
                  v-model.number="customYears"
                  type="number"
                  min="1"
                  max="10"
                  class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  @click="selectCustomYears"
                  class="px-3 py-2 text-sm bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Apply
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="clearDates"
            class="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            Clear
          </button>
          <button
            @click="closePicker"
            class="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Done
          </button>
        </div>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="isOpen"
      @click="closePicker"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface DateRange {
  startDate: string | null
  endDate: string | null
}

interface Preset {
  label: string
  getValue: () => DateRange
}

// Props
interface Props {
  modelValue?: DateRange
  placeholder?: string
  position?: 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ startDate: null, endDate: null }),
  placeholder: 'Select date range',
  position: 'left'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: DateRange]
  'change': [value: DateRange]
}>()

// Local state
const isOpen = ref(false)
const localStartDate = ref<string>('')
const localEndDate = ref<string>('')
const customMonths = ref<number>(3)
const customYears = ref<number>(1)

// Utility functions
const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0]
}

const formatDisplayDate = (dateStr: string): string => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  })
}

// Presets
const presets: Preset[] = [
  {
    label: 'Today',
    getValue: () => {
      const today = formatDate(new Date())
      return { startDate: today, endDate: today }
    }
  },
  {
    label: 'Last 7 days',
    getValue: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'Last 14 days',
    getValue: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 13)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'Last 30 days',
    getValue: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'This month',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1)
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'Last month',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const end = new Date(now.getFullYear(), now.getMonth(), 0)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'This year',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), 0, 1)
      const end = new Date(now.getFullYear(), 11, 31)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  },
  {
    label: 'Last year',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear() - 1, 0, 1)
      const end = new Date(now.getFullYear() - 1, 11, 31)
      return { startDate: formatDate(start), endDate: formatDate(end) }
    }
  }
]

// Computed
const displayText = computed(() => {
  if (props.modelValue.startDate && props.modelValue.endDate) {
    if (props.modelValue.startDate === props.modelValue.endDate) {
      return formatDisplayDate(props.modelValue.startDate)
    }
    return `${formatDisplayDate(props.modelValue.startDate)} - ${formatDisplayDate(props.modelValue.endDate)}`
  }
  return props.placeholder
})

const dropdownPosition = computed(() => {
  return props.position === 'right' ? 'right-0' : 'left-0'
})

// Methods
const togglePicker = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    localStartDate.value = props.modelValue.startDate || ''
    localEndDate.value = props.modelValue.endDate || ''
  }
}

const closePicker = () => {
  isOpen.value = false
}

const selectPreset = (preset: Preset) => {
  const range = preset.getValue()
  updateRange(range)
  closePicker()
}

const selectCustomMonths = () => {
  if (customMonths.value >= 2 && customMonths.value <= 12) {
    const end = new Date()
    const start = new Date()
    start.setMonth(start.getMonth() - customMonths.value)
    const range = { startDate: formatDate(start), endDate: formatDate(end) }
    updateRange(range)
    closePicker()
  }
}

const selectCustomYears = () => {
  if (customYears.value >= 1) {
    const end = new Date()
    const start = new Date()
    start.setFullYear(start.getFullYear() - customYears.value)
    const range = { startDate: formatDate(start), endDate: formatDate(end) }
    updateRange(range)
    closePicker()
  }
}

const onDateChange = () => {
  // Auto-fill logic: if one date is selected and the other is empty, fill with same date
  if (localStartDate.value && !localEndDate.value) {
    localEndDate.value = localStartDate.value
  } else if (localEndDate.value && !localStartDate.value) {
    localStartDate.value = localEndDate.value
  }
  
  // Update the range
  const range = { startDate: localStartDate.value || null, endDate: localEndDate.value || null }
  updateRange(range)
}

const updateRange = (range: DateRange) => {
  emit('update:modelValue', range)
  emit('change', range)
}

const clearDates = () => {
  localStartDate.value = ''
  localEndDate.value = ''
  updateRange({ startDate: null, endDate: null })
}

// Handle clicks outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    closePicker()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  localStartDate.value = newValue.startDate || ''
  localEndDate.value = newValue.endDate || ''
}, { deep: true })
</script>
