<template>
  <div v-if="show" :class="containerClasses">
    <div v-if="overlay" class="absolute inset-0 bg-white dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 z-40"></div>
    <div :class="spinnerWrapperClasses">
      <!-- Spinner -->
      <div :class="spinnerClasses">
        <div v-for="i in 12" :key="i" :class="barClasses" :style="getBarStyle(i)"></div>
      </div>
      
      <!-- Loading text -->
      <div v-if="text" :class="textClasses">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'

// Props
const props = defineProps<{
  show?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
  text?: string
  overlay?: boolean
  center?: boolean
  fullScreen?: boolean
}>()

// Computed properties
const sizeConfig = computed(() => {
  const configs = {
    xs: { spinner: 'w-4 h-4', bar: 'w-0.5 h-1', text: 'text-xs' },
    sm: { spinner: 'w-6 h-6', bar: 'w-0.5 h-1.5', text: 'text-sm' },
    md: { spinner: 'w-8 h-8', bar: 'w-1 h-2', text: 'text-base' },
    lg: { spinner: 'w-12 h-12', bar: 'w-1 h-3', text: 'text-lg' },
    xl: { spinner: 'w-16 h-16', bar: 'w-1.5 h-4', text: 'text-xl' }
  }
  return configs[props.size || 'md']
})

const colorConfig = computed(() => {
  const configs = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600',
    purple: 'bg-purple-600',
    gray: 'bg-gray-600'
  }
  return configs[props.color || 'blue']
})

const containerClasses = computed(() => {
  const classes = []
  
  if (props.fullScreen) {
    classes.push('fixed inset-0 z-50')
  } else if (props.overlay) {
    classes.push('absolute inset-0 z-40')
  }
  
  if (props.center || props.fullScreen) {
    classes.push('flex items-center justify-center')
  }
  
  return classes.join(' ')
})

const spinnerWrapperClasses = computed(() => {
  const classes = ['flex flex-col items-center space-y-2']
  
  if (props.fullScreen) {
    classes.push('bg-white dark:bg-gray-800 bg-opacity-90 dark:bg-opacity-90 p-8 rounded-lg shadow-lg')
  } else if (props.center && !props.overlay) {
    classes.push('p-4')
  }
  
  return classes.join(' ')
})

const spinnerClasses = computed(() => {
  return [
    'relative animate-spin',
    sizeConfig.value.spinner
  ].join(' ')
})

const barClasses = computed(() => {
  return [
    'absolute rounded-full',
    sizeConfig.value.bar,
    colorConfig.value
  ].join(' ')
})

const textClasses = computed(() => {
  return [
    'text-gray-600 font-medium',
    sizeConfig.value.text
  ].join(' ')
})

// Methods
const getBarStyle = (index: number) => {
  const angle = (index - 1) * 30 // 360 / 12 = 30 degrees
  const delay = (index - 1) * 0.083 // 1s / 12 = 0.083s
  
  return {
    transform: `rotate(${angle}deg) translate(0, -150%)`,
    transformOrigin: '50% 100%',
    animationDelay: `${delay}s`,
    opacity: 0.1 + (index / 12) * 0.9
  }
}
</script>

<style scoped>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Individual bar animation */
.absolute {
  animation: fade 1s linear infinite;
}

@keyframes fade {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 1;
  }
}
</style>
