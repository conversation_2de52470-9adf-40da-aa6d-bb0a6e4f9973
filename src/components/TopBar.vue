<template>
  <header class="bg-gradient-to-br from-[#20ad79]/20 to-[#182232]/20 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm relative z-30">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6">
      <!-- Left side: Hamburger menu and page title -->
      <div class="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
        <!-- Hamburger Menu Button -->
        <button
          @click="sidebarStore.toggle()"
          class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 flex-shrink-0"
          aria-label="Toggle sidebar"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>

        <!-- Page Title -->
        <div class="flex items-center space-x-2 min-w-0 flex-1">
          <h1 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate">{{ pageTitle }}</h1>
          <div v-if="breadcrumbs.length > 1" class="hidden sm:flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-300">
            <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span class="truncate">{{ breadcrumbs.join(' / ') }}</span>
          </div>
        </div>
      </div>

      <!-- Right side: Search, notifications, and user menu -->
      <div class="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
        <!-- Partner Search Bar (Admin Only) -->
        <div v-if="authStore.isSuperUser" class="relative hidden lg:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <input
            v-model="partnerSearchQuery"
            @input="debouncedPartnerSearch"
            @focus="showPartnerSearchResults = true"
            type="text"
            placeholder="Search partners..."
            class="block w-48 xl:w-64 pl-10 pr-10 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
          <!-- Clear button -->
          <button
            v-if="partnerSearchQuery"
            @click="clearPartnerSearch"
            class="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>

          <!-- Partner Search Results Dropdown -->
          <div v-if="showPartnerSearchResults && (partnerSearchResults.length > 0 || partnerSearchQuery)"
               class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
            <!-- All Partners Option -->
            <button
              @click="selectPartnerFromSearch('all')"
              class="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center border-b border-gray-100"
            >
              <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <div>
                <div class="font-medium text-blue-600">All Partners</div>
                <div class="text-xs text-gray-500">View data from all partners</div>
              </div>
            </button>

            <!-- Search Results -->
            <div v-if="partnerSearchResults.length > 0">
              <button
                v-for="partner in partnerSearchResults"
                :key="partner.id"
                @click="selectPartnerFromSearch(partner.id.toString())"
                class="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center"
              >
                <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <div>
                  <div class="font-medium">{{ partner.name }}</div>
                  <div class="text-xs text-gray-500">ID: {{ partner.id }} • {{ partner.country || 'No country' }}</div>
                </div>
              </button>
            </div>

            <!-- No Results -->
            <div v-else-if="partnerSearchQuery && !partnerSearchLoading" class="px-4 py-3 text-sm text-gray-500">
              No partners found for "{{ partnerSearchQuery }}"
            </div>

            <!-- Loading -->
            <div v-if="partnerSearchLoading" class="px-4 py-3 text-sm text-gray-500 flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              Searching partners...
            </div>
          </div>
        </div>

        <!-- Mobile Search Button -->
        <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 lg:hidden">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </button>

        <!-- Theme Toggle -->
        <button
          @click="themeStore.toggleTheme()"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          :title="themeTooltip"
        >
          <!-- Light mode icon -->
          <svg v-if="themeStore.currentTheme === 'light'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
          </svg>
          <!-- Dark mode icon -->
          <svg v-else-if="themeStore.currentTheme === 'dark'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
          </svg>
          <!-- System mode icon -->
          <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
        </button>

        <!-- Notifications -->
        <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 relative">
          <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z"/>
          </svg>
          <!-- Notification badge -->
          <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
        </button>

        <!-- Partner Selection (if multiple partners) -->
        <div v-if="authStore.hasMultiplePartners" class="relative mr-2">
          <button @click="showPartnerDropdown = !showPartnerDropdown"
            class="flex items-center space-x-2 px-3 py-2 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            <span class="max-w-32 truncate">{{ getSelectedPartnersText() }}</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- Partner Dropdown -->
          <div v-if="showPartnerDropdown" class="absolute right-0 z-50 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div class="py-1">
              <!-- Select All Option -->
              <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                <label class="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    :checked="isAllPartnersSelected()"
                    @change="toggleAllPartners"
                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  >
                  <div class="ml-3">
                    <div class="font-medium text-gray-900 dark:text-white">All Partners</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">Select all available partners</div>
                  </div>
                </label>
              </div>
              <!-- Individual Partners -->
              <div
                v-for="partner in authStore.partnerList"
                :key="partner.id"
                class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <label class="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    :checked="isPartnerSelected(partner.id.toString())"
                    @change="togglePartner(partner.id.toString())"
                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  >
                  <div class="ml-3">
                    <div class="font-medium text-gray-900 dark:text-white">{{ partner.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">ID: {{ partner.id }}</div>
                  </div>
                </label>
              </div>
          </div>
        </div>

        <!-- User Menu -->
        <div class="relative">
          <button
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-2 sm:space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-white text-sm font-medium">{{ userInitials }}</span>
            </div>
            <div class="hidden sm:block text-left min-w-0">
              <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ userName }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-300 truncate">{{ userRoleName || companyName || userRole }}</div>
            </div>
            <svg class="w-4 h-4 text-gray-400 hidden sm:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>

          <!-- User Dropdown Menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
            >
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>

              <!-- Privacy Mode Toggle -->
              <button
                @click="authStore.togglePrivateMode()"
                class="w-full text-left flex items-center justify-between px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <span class="flex items-center">
                  <span class="mr-2">{{ authStore.isPrivateMode ? '👁️‍🗨️' : '👁️' }}</span>
                  Privacy Mode
                </span>
                <span class="text-xs px-2 py-1 rounded-full" :class="authStore.isPrivateMode ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'">
                  {{ authStore.isPrivateMode ? 'ON' : 'OFF' }}
                </span>
              </button>

              <hr class="my-1">
              <button
                @click="handleLogout"
                :disabled="isLoggingOut"
                class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isLoggingOut ? 'Signing out...' : 'Sign out' }}
              </button>
            </div>
          </Transition>
        </div>
      </div>
    </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import { partnerApi } from '@/services/partnerApi'

const route = useRoute()
const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()
const themeStore = useThemeStore()
const showUserMenu = ref(false)
const showPartnerDropdown = ref(false)
const isLoggingOut = ref(false)

// Partner search functionality
const partnerSearchQuery = ref('')
const partnerSearchResults = ref<any[]>([])
const showPartnerSearchResults = ref(false)
const partnerSearchLoading = ref(false)
let partnerSearchTimeout: ReturnType<typeof setTimeout> | null = null

// Page titles mapping
const pageTitles: Record<string, string> = {
  'dashboard': 'Dashboard',
  'organisations': 'Organizations',
  'organisations-config': 'Organization Configuration',
  'organisations-bulk': 'Organization Bulk SMS',
  'clients': 'Clients',
  'clients-config': 'Client Configuration',
  'clients-bulk': 'Client Bulk SMS',
  'merchants': 'Merchants',
  'merchants-config': 'Merchant Configuration',
  'merchants-bulk': 'Merchant Bulk SMS',
  'requests': 'Loan Requests',
  'limits': 'Loan Limits',
  'check-off': 'Check-off',
  'loan-accounts': 'Loan Accounts',
  'loan-products': 'Loan Products',
  'loan-repayments': 'Loan Repayments',
  'transactions': 'Transactions',
  'withdrawals': 'Withdrawals',
  'bill-payments': 'Bill Payments',
  'bill-payments-add': 'Add Bill Payment',
  'customers': 'Customer Search',
  'partners': 'Partners',
  'partner-services': 'Partner Services',
  'partners-bets': 'Partners Bets',
  'partners-bet-slips': 'Partners Bet Slips',
  'services': 'Services',
  'system-roles': 'System Roles',
  'system-permissions': 'System Permissions'
}

const pageTitle = computed(() => {
  return pageTitles[route.name as string] || 'Mossbets B2B Dashboard'
})

const userName = computed(() => {
  return authStore.user?.display_name || authStore.user?.username || 'User'
})

const userInitials = computed(() => {
  const name = userName.value
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
})

const userRoleName = computed(() => {
  return authStore.user?.rname || authStore.user?.role_name || ''
})

const userRole = computed(() => {
  return authStore.user?.user_type || 'User'
})

const companyName = computed(() => {
  return authStore.user?.cn || ''
})

const breadcrumbs = computed(() => {
  const routeName = route.name as string
  const breadcrumbMap: Record<string, string[]> = {
    'partners': ['Partners'],
    'partner-services': ['Partners', 'Services'],
    'partners-bets': ['Partners', 'Bets'],
    'partners-bet-slips': ['Partners', 'Bet Slips'],
    'services': ['Services'],
    'system-roles': ['System', 'Roles'],
    'system-permissions': ['System', 'Permissions']
  }

  return breadcrumbMap[routeName] || []
})

const themeTooltip = computed(() => {
  const themeLabels = {
    light: 'Switch to dark mode',
    dark: 'Switch to system mode',
    system: 'Switch to light mode'
  }
  return themeLabels[themeStore.theme]
})

// Multi-partner selection state
const selectedPartnerIds = ref<string[]>([])

onMounted(() => {
  if (authStore.selectedPartnerId === 'all') {
    selectedPartnerIds.value = authStore.partnerList.map(p => p.id.toString())
  } else if (authStore.selectedPartnerId) {
    selectedPartnerIds.value = [authStore.selectedPartnerId]
  }
})




const isPartnerSelected = (partnerId: string): boolean => {
  return selectedPartnerIds.value.includes(partnerId)
}

const isAllPartnersSelected = (): boolean => {
  return selectedPartnerIds.value.length === authStore.partnerList.length
}

const togglePartner = (partnerId: string) => {
  if (selectedPartnerIds.value.includes(partnerId)) {
    selectedPartnerIds.value = selectedPartnerIds.value.filter(id => id !== partnerId)
  } else {
    selectedPartnerIds.value.push(partnerId)
  }
  updateSelectedPartners()
}

const toggleAllPartners = () => {
  if (isAllPartnersSelected()) {
    selectedPartnerIds.value = []
  } else {
    selectedPartnerIds.value = authStore.partnerList.map(p => p.id.toString())
  }
  updateSelectedPartners()
}

const updateSelectedPartners = async () => {
  if (selectedPartnerIds.value.length === 0) {
    await authStore.selectPartner('')
  } else if (selectedPartnerIds.value.length === authStore.partnerList.length) {
    await authStore.selectPartner('all')
  } else {
    await authStore.selectPartner(selectedPartnerIds.value.join(','))
  }
}

const getSelectedPartnersText = (): string => {
  if (selectedPartnerIds.value.length === 0) {
    return 'No Partners'
  } else if (selectedPartnerIds.value.length === authStore.partnerList.length) {
    return 'All Partners'
  } else if (selectedPartnerIds.value.length === 1) {
    const partner = authStore.partnerList.find(p => p.id.toString() === selectedPartnerIds.value[0])
    return partner?.name || 'Select Partner'
  } else {
    return `${selectedPartnerIds.value.length} Partners`
  }
}


const searchPartners = async () => {
  if (!partnerSearchQuery.value.trim()) {
    partnerSearchResults.value = []
    return
  }

  partnerSearchLoading.value = true
  try {
    const response = await partnerApi.getPartners({
      search: partnerSearchQuery.value.trim(),
      limit: 10,
      partner_ids: ''
    })

    if (response.status === 200) {
      partnerSearchResults.value = response.message?.data || []
    } else {
      partnerSearchResults.value = []
    }
  } catch (error) {
    console.error('Partner search error:', error)
    partnerSearchResults.value = []
  } finally {
    partnerSearchLoading.value = false
  }
}

const debouncedPartnerSearch = () => {
  if (partnerSearchTimeout) {
    clearTimeout(partnerSearchTimeout)
  }

  partnerSearchTimeout = setTimeout(() => {
    searchPartners()
  }, 300)
}

const selectPartnerFromSearch = async (partnerId: string) => {
  await authStore.selectPartner(partnerId)
  partnerSearchQuery.value = ''
  partnerSearchResults.value = []
  showPartnerSearchResults.value = false
}

const clearPartnerSearch = () => {
  partnerSearchQuery.value = ''
  partnerSearchResults.value = []
  showPartnerSearchResults.value = false
  authStore.selectPartner('all')
}


const handleLogout = async () => {
  if (isLoggingOut.value) return

  try {
    isLoggingOut.value = true
    showUserMenu.value = false
    await authStore.logout()
  } catch (error) {
    console.error('Logout error:', error)
    router.push({ name: 'login' })
  } finally {
    isLoggingOut.value = false
  }
}


const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
    showPartnerDropdown.value = false
    showPartnerSearchResults.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
