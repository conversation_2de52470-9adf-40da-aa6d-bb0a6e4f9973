<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
    @click="closeModal"
  >
    <div
      class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 xl:w-1/3 shadow-lg rounded-md bg-white"
      @click.stop
    >
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Select Partner Account
        </h3>
        <button
          v-if="allowClose"
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="mt-4">
        <p class="text-sm text-gray-600 mb-4">
          You have access to multiple partner accounts. Please select which account you'd like to work with:
        </p>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Partner List -->
        <div v-else class="space-y-3 max-h-96 overflow-y-auto">
          <!-- Show All Option -->
          <div
            v-if="showAllOption"
            @click="selectPartner(null)"
            class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200"
            :class="{ 'bg-blue-50 border-blue-300': selectedPartnerId === null }"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="text-sm font-medium text-gray-900">Show All Partners</h4>
                <p class="text-sm text-gray-500">Access data from all partner accounts</p>
              </div>
            </div>
          </div>

          <!-- Individual Partners -->
          <div
            v-for="partner in partners"
            :key="partner.id"
            @click="selectPartner(partner.id)"
            class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200"
            :class="{ 'bg-blue-50 border-blue-300': selectedPartnerId === partner.id }"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {{ getPartnerInitials(partner.name) }}
                  </span>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <h4 class="text-sm font-medium text-gray-900">{{ partner.name }}</h4>
                <p class="text-sm text-gray-500">{{ partner.description || 'Partner Account' }}</p>
                <div v-if="partner.status" class="mt-1">
                  <span
                    :class="[
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      partner.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ partner.status === 1 ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-if="error" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading partners</h3>
          <p class="mt-1 text-sm text-gray-500">{{ error }}</p>
          <button
            @click="error = ''"
            class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Dismiss
          </button>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-6 flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          v-if="allowClose"
          @click="closeModal"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          @click="confirmSelection"
          :disabled="selectedPartnerId === undefined || confirming"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="confirming" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          {{ confirming ? 'Selecting...' : 'Continue' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  isOpen: boolean
  partnerIds?: number[]
  allowClose?: boolean
  showAllOption?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  allowClose: true,
  showAllOption: true
})

// Emits
const emit = defineEmits<{
  close: []
  selected: [partnerId: number | null]
}>()

// Reactive data
const authStore = useAuthStore()
const loading = ref(false)
const confirming = ref(false)
const error = ref('')
const selectedPartnerId = ref<number | null | undefined>(undefined)

// Get partners from auth store instead of fetching from API
const partners = computed(() => authStore.partnerList || [])

// Methods - Partners are now loaded from auth store, no need to fetch from API

const selectPartner = (partnerId: number | null) => {
  selectedPartnerId.value = partnerId
}

const confirmSelection = async () => {
  if (selectedPartnerId.value === undefined) return
  
  confirming.value = true
  try {
    emit('selected', selectedPartnerId.value)
  } finally {
    confirming.value = false
  }
}

const closeModal = () => {
  if (props.allowClose) {
    emit('close')
  }
}

const getPartnerInitials = (name: string): string => {
  if (!name) return 'P'
  const parts = name.split(' ')
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase()
  }
  return name.substring(0, 2).toUpperCase()
}

// Lifecycle - No longer needed since partners come from auth store
</script>
