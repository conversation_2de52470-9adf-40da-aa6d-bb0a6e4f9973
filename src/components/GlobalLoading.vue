<template>
  <!-- Global Loading Overlay -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="loadingStore.isGlobalLoading"
        class="fixed inset-0 z-[9999] flex items-center justify-center"
        role="dialog"
        aria-modal="true"
        aria-labelledby="loading-title"
      >
        <!-- Backdrop -->
        <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
        
        <!-- Loading Content -->
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 mx-4 max-w-sm w-full">
          <!-- Loading Animation -->
          <div class="flex flex-col items-center space-y-4">
            <!-- Spinner -->
            <div class="relative">
              <div class="w-12 h-12 border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin border-t-blue-600 dark:border-t-blue-400"></div>
              
              <!-- Progress Ring (if progress is available) -->
              <div v-if="loadingStore.loadingProgress !== undefined" class="absolute inset-0">
                <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 48 48">
                  <circle
                    cx="24"
                    cy="24"
                    r="20"
                    stroke="currentColor"
                    stroke-width="4"
                    fill="none"
                    class="text-gray-200 dark:text-gray-700"
                  />
                  <circle
                    cx="24"
                    cy="24"
                    r="20"
                    stroke="currentColor"
                    stroke-width="4"
                    fill="none"
                    stroke-linecap="round"
                    class="text-blue-600 dark:text-blue-400 transition-all duration-300"
                    :stroke-dasharray="circumference"
                    :stroke-dashoffset="progressOffset"
                  />
                </svg>
                
                <!-- Progress Percentage -->
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-xs font-medium text-gray-600 dark:text-gray-300">
                    {{ Math.round(loadingStore.loadingProgress) }}%
                  </span>
                </div>
              </div>
            </div>

            <!-- Loading Message -->
            <div class="text-center">
              <h3 
                id="loading-title"
                class="text-lg font-medium text-gray-900 dark:text-white mb-1"
              >
                {{ loadingStore.currentLoadingMessage }}
              </h3>
              
              <!-- Active Operations Count -->
              <p 
                v-if="loadingStore.activeLoadingCount > 1"
                class="text-sm text-gray-500 dark:text-gray-400"
              >
                {{ loadingStore.activeLoadingCount }} operations in progress
              </p>
              
              <!-- Progress Bar (alternative to ring) -->
              <div 
                v-if="loadingStore.loadingProgress !== undefined && showProgressBar"
                class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3"
              >
                <div 
                  class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${loadingStore.loadingProgress}%` }"
                ></div>
              </div>
            </div>

            <!-- Animated Dots -->
            <div class="flex space-x-1">
              <div 
                v-for="i in 3" 
                :key="i"
                class="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"
                :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useLoadingStore } from '@/stores/loading'

interface Props {
  showProgressBar?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showProgressBar: false
})

const loadingStore = useLoadingStore()

// Progress circle calculations
const radius = 20
const circumference = computed(() => 2 * Math.PI * radius)
const progressOffset = computed(() => {
  if (loadingStore.loadingProgress === undefined) return circumference.value
  const progress = Math.max(0, Math.min(100, loadingStore.loadingProgress))
  return circumference.value - (progress / 100) * circumference.value
})
</script>

<style scoped>
/* Custom animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Ensure the overlay is above everything */
.z-\[9999\] {
  z-index: 9999;
}

/* Backdrop blur for modern browsers */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Fallback for older browsers */
@supports not (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    background-color: rgba(0, 0, 0, 0.7);
  }
}
</style>
