<template>
  <!-- Sidebar -->
  <aside
    class="bg-gray-400 bg-gradient-to-br from-[#20ad79]/80 to-[#182232]/80 from-slate-800 to-slate-900 shadow-xl sidebar-transition flex flex-col overflow-hidden"
    :class="[
      sidebarStore.sidebarWidth,
      {
        'fixed inset-y-0 left-0 z-50': sidebarStore.isMobile,
        'fixed inset-y-0 left-0 z-20': !sidebarStore.isMobile,
        'transform transition-transform duration-300 ease-in-out': true,
        'translate-x-0': sidebarStore.isMobile ? sidebarStore.isOpen : true,
        '-translate-x-full': sidebarStore.isMobile && !sidebarStore.isOpen
      }
    ]"
  >
    <!-- Sidebar Header -->
    <div class="relative overflow-hidden flex-shrink-0">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-br from-[#20ad79]/20 to-[#182232]/20"></div>
      <div class="absolute inset-0 opacity-30 sidebar-pattern"></div>

      <!-- <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20"></div>
      <div class="absolute inset-0 opacity-30 sidebar-pattern"></div> -->

      <!-- Header Content -->
      <div class="relative z-10 px-4 py-6 border-b border-white/10">
        <div class="flex items-center space-x-3">
          <!-- Logo Icon -->
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">S</span>
            </div>
          </div>

          <!-- Brand Text -->
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 transform translate-x-4"
            enter-to-class="opacity-100 transform translate-x-0"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 transform translate-x-0"
            leave-to-class="opacity-0 transform translate-x-4"
          >
            <div v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 min-w-0">
              <h1 class="text-xl font-bold text-white tracking-tight">
                Mossbets B2B
              </h1>
              <p class="text-xs text-slate-300 mt-1">Partner Dashboard</p>
            </div>
          </Transition>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
      <!-- Dashboard -->
      <router-link
        :to="{ name: 'dashboard' }"
        class="sidebar-item group"
        :class="{ 'sidebar-item-active': route.name === 'dashboard' }"
        @click="handleMenuClick"
      >
        <HomeIcon class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" />
        <Transition
          enter-active-class="transition-all duration-300"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="transition-all duration-300"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">Dashboard</span>
        </Transition>
        
        <!-- Tooltip for collapsed state -->
        <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
             class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]"
             style="z-index: 9999 !important;">
          Dashboard
        </div>
      </router-link>

      <!-- Main Navigation -->
      <div class="space-y-1">
        <div v-if="sidebarStore.isOpen || sidebarStore.isMobile"
             class="px-2 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
          Partners & Bets
        </div>


        <!-- Partners Section -->
        <div class="relative group">
          <button
            @click="handlePartnersClick"
            @mouseenter="handlePartnersHover"
            @mouseleave="handlePartnersLeave"
            class="sidebar-item w-full"
            :class="{ 'sidebar-item-active': isPartnersActive }"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 text-left">Partners</span>
            </Transition>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0 rotate-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0 rotate-0"
            >
              <ChevronRightIcon
                v-if="sidebarStore.isOpen || sidebarStore.isMobile"
                class="w-4 h-4 transition-transform duration-200"
                :class="{ 'rotate-90': sidebarStore.partnersMenuOpen }"
              />
            </Transition>
          </button>

          <!-- Partners Submenu -->
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 max-h-96"
            leave-to-class="opacity-0 max-h-0"
          >
            <div v-if="sidebarStore.partnersMenuOpen && (sidebarStore.isOpen || sidebarStore.isMobile)" class="overflow-hidden">
              <div class="ml-8 mt-2 space-y-1">
                <router-link
                  :to="{ name: 'partners' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partners' }"
                  @click="handleMenuClick"
                >
                  All Partners
                </router-link>
                <router-link
                  :to="{ name: 'partners-bets' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partners-bets' }"
                  @click="handleMenuClick"
                >
                  Partners Bets
                </router-link>
                <router-link
                  :to="{ name: 'partners-bet-slips' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partners-bet-slips' }"
                  @click="handleMenuClick"
                >
                  Partners Bet Slips
                </router-link>
                <router-link
                  :to="{ name: 'partners-balances' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partners-balances' }"
                  @click="handleMenuClick"
                >
                  Partners Balances
                </router-link>
                <router-link
                  :to="{ name: 'partner-services' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partner-services' }"
                  @click="handleMenuClick"
                >
                  Partner Services
                </router-link>
                <router-link
                  :to="{ name: 'partners-settings' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': route.name === 'partners-settings' }"
                  @click="handleMenuClick"
                >
                  Partners Settings
                </router-link>
                <!-- <router-link
                  :to="{ name: 'partners-bet-slips' }"
                  class="sidebar-submenu-item"
                  :class="{ 'sidebar-submenu-item-active': $route.name === 'partners-bet-slips' }"
                  @click="handleMenuClick"
                >
                  Bet Slips
                </router-link> -->
              </div>
            </div>
          </Transition>

          <!-- Dropdown menu for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen && sidebarStore.partnersMenuOpen"
               @mouseenter="handleDropdownEnter"
               @mouseleave="handleDropdownLeave"
               class="absolute left-full ml-2 top-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl py-2 min-w-48 sidebar-dropdown"
               style="z-index: 99999 !important; transform: translateZ(0);"
               :style="{ position: 'fixed' }">
            <router-link
              :to="{ name: 'partners' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              All Partners
            </router-link>
            <router-link
              :to="{ name: 'partners-bets' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              Partners Bets
            </router-link>
            <router-link
              :to="{ name: 'partners-bet-slips' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              Partners Bet Slips
            </router-link>
            <router-link
              :to="{ name: 'partners-balances' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              Partners Balances
            </router-link>
            <router-link
              :to="{ name: 'partner-services' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              Partner Services
            </router-link>
            <router-link
              :to="{ name: 'partners-settings' }"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              @click="handleMenuClick"
            >
              Partners Settings
            </router-link>
          </div>

          <!-- Simple tooltip when menu is not open -->
          <div v-else-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]"
               style="z-index: 9999 !important;">
            Partners
          </div>
        </div>

        <!-- Services Section -->
        <div class="relative group">
          <router-link
            :to="{ name: 'services' }"
            class="sidebar-item"
            :class="{ 'sidebar-item-active': route.name === 'services' }"
            @click="handleMenuClick"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">Services</span>
            </Transition>
          </router-link>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]"
               style="z-index: 9999 !important;">
            Services
          </div>
        </div>

        <!-- System with Submenu -->
        <div v-if="authStore.isSuperUser || [1, 2].includes(authStore.role || 0)" :to="{ name: 'system-settings' }"  class="relative group">
          <button
            @click="sidebarStore.toggleSystemMenu()"
            class="sidebar-item w-full"
            :class="{ 'sidebar-item-active': isSystemActive }"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 text-left">System</span>
            </Transition>
            <ChevronDownIcon
              v-if="sidebarStore.isOpen || sidebarStore.isMobile"
              class="w-4 h-4 transition-transform duration-200"
              :class="{ 'rotate-180': sidebarStore.systemMenuOpen }"
            />
          </button>

          <!-- System Submenu -->
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="opacity-100 max-h-96"
            leave-to-class="opacity-0 max-h-0"
          >
            <div v-if="(sidebarStore.isOpen || sidebarStore.isMobile) && sidebarStore.systemMenuOpen"
                 class="overflow-hidden submenu-container">
              <!-- Temporarily commented out due to template parsing issue -->
              <router-link :to="{ name: 'system-users' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-users', 'add-user', 'edit-user'].includes(route.name as string) }" @click="handleMenuClick">
                Users
              </router-link> 

              <router-link :to="{ name: 'system-roles' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-roles', 'add-role', 'edit-role'].includes(route.name as string) }" @click="handleMenuClick">
                Roles
              </router-link>
              
              <router-link :to="{ name: 'system-permissions' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-permissions', 'add-permission', 'edit-permission'].includes(route.name as string) }" @click="handleMenuClick">
                Permissions
              </router-link>

              <!-- <router-link v-if="authStore.isSuperUser || [1, 2].includes(authStore.role || 0)" :to="{ name: 'system-settings' }" class="submenu-item" :class="{ 'submenu-item-active': route.name === 'system-settings' }" @click="handleMenuClick">
                Settings
              </router-link> -->
            </div>
          </Transition>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]"
               style="z-index: 9999 !important;">
            <div class="font-medium mb-1">System</div>
            <div class="text-xs space-y-1">
              <div>• Users</div>
              <div>• Roles</div>
              <div>• Permissions</div>
            </div>
          </div>
        </div>

      </div>
    </nav>

    <!-- Logout Button -->
    <div class="flex-shrink-0 p-3 border-t border-white/10">
      <div class="relative group">
        <button
          @click="handleLogout"
          :disabled="isLoggingOut"
          class="sidebar-item w-full text-red-300 hover:text-red-200 hover:bg-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowRightOnRectangleIcon
            class="w-5 h-5"
            :class="{
              'mr-3': sidebarStore.isOpen || sidebarStore.isMobile,
              'animate-spin': isLoggingOut
            }"
          />
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">
              {{ isLoggingOut ? 'Signing Out...' : 'Sign Out' }}
            </span>
          </Transition>
        </button>

        <!-- Tooltip for collapsed state -->
        <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
             class="absolute left-full ml-2 bottom-0 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]"
             style="z-index: 9999 !important;">
          Sign Out
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'
import {
  HomeIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()

// Logout state
const isLoggingOut = ref(false)

// Computed properties for active states

const isSystemActive = computed(() => {
  return ['system-users', 'add-user', 'edit-user', 'system-roles', 'add-role', 'edit-role', 'system-permissions', 'add-permission', 'edit-permission', 'system-settings'].includes(route.name as string)
})

const isPartnersActive = computed(() => {
  return ['partners', 'partner-services','partners-balances', 'partners-bets','partner-add','partner-edit','partner-details', 'partners-bet-slips', 'partners-settings'].includes(route.name as string)
})

// Handle Partners menu interactions
const handlePartnersClick = () => {
  if (sidebarStore.isOpen || sidebarStore.isMobile) {
    // If sidebar is open, toggle the submenu
    sidebarStore.togglePartnersMenu()
  } else {
    // If sidebar is collapsed, navigate directly to All Partners
    router.push({ name: 'partners' })
  }
}

let hoverTimeout: number | null = null

const handlePartnersHover = () => {
  if (!sidebarStore.isOpen && !sidebarStore.isMobile) {
    // Clear any pending hide timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      hoverTimeout = null
    }
    // Show submenu on hover when collapsed
    sidebarStore.partnersMenuOpen = true
  }
}

const handlePartnersLeave = () => {
  if (!sidebarStore.isOpen && !sidebarStore.isMobile) {
    // Hide submenu when mouse leaves with delay
    hoverTimeout = setTimeout(() => {
      sidebarStore.partnersMenuOpen = false
      hoverTimeout = null
    }, 300) // Increased delay to allow moving to submenu
  }
}

const handleDropdownEnter = () => {
  // Keep dropdown open when mouse enters
  if (hoverTimeout) {
    clearTimeout(hoverTimeout)
    hoverTimeout = null
  }
  sidebarStore.partnersMenuOpen = true
}

const handleDropdownLeave = () => {
  // Hide dropdown when mouse leaves
  if (!sidebarStore.isOpen && !sidebarStore.isMobile) {
    hoverTimeout = setTimeout(() => {
      sidebarStore.partnersMenuOpen = false
      hoverTimeout = null
    }, 200)
  }
}

// Handle menu click - close sidebar on mobile
const handleMenuClick = () => {
  if (sidebarStore.isMobile && sidebarStore.isOpen) {
    sidebarStore.close()
  }
}

// Handle logout
const handleLogout = async () => {
  if (isLoggingOut.value) return

  try {
    isLoggingOut.value = true
    await authStore.logout()
    // Navigation will be handled by the auth store
  } catch (error) {
    console.error('Logout error:', error)
    // Force navigation to login even if logout fails
    router.push({ name: 'login' })
  } finally {
    isLoggingOut.value = false
  }
}

// Auto-expand menus based on current route
const handleRouteChange = () => {
  sidebarStore.autoExpandMenus(route.name as string)
}

// Handle window resize for mobile detection
const handleResize = () => {
  const isMobile = window.innerWidth < 768
  sidebarStore.setMobile(isMobile)
}

onMounted(() => {
  handleRouteChange()
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// Watch for route changes
import { watch } from 'vue'
watch(() => route.name, handleRouteChange)
</script>

<style scoped>
.sidebar-dropdown {
  position: absolute !important;
  z-index: 99999 !important;
  transform: translateZ(0);
  will-change: transform;
}

/* Ensure the dropdown appears above all content */
.sidebar-dropdown::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  pointer-events: none;
}

/* Fix z-index for all sidebar dropdowns and tooltips */
:deep(.absolute) {
  z-index: 99999 !important;
}

/* Ensure sidebar container has proper stacking context */
.sidebar-container {
  position: relative;
  z-index: 9999;
}
</style>
