/**
 * Centralized Theme Configuration
 * 
 * This file contains all theme-related configurations including:
 * - Color palettes for light and dark modes
 * - Component-specific styling
 * - CSS custom properties
 * - Tailwind class mappings
 */

export interface ThemeColors {
  // Background colors
  background: {
    primary: string
    secondary: string
    tertiary: string
    elevated: string
  }
  
  // Text colors
  text: {
    primary: string
    secondary: string
    tertiary: string
    inverse: string
  }
  
  // Border colors
  border: {
    primary: string
    secondary: string
    focus: string
  }
  
  // Interactive colors
  interactive: {
    primary: string
    primaryHover: string
    secondary: string
    secondaryHover: string
    danger: string
    dangerHover: string
    success: string
    warning: string
  }
  
  // Status colors
  status: {
    active: string
    inactive: string
    pending: string
    error: string
  }
}

export const lightTheme: ThemeColors = {
  background: {
    primary: 'bg-white',
    secondary: 'bg-gray-50',
    tertiary: 'bg-gray-100',
    elevated: 'bg-white'
  },
  text: {
    primary: 'text-gray-900',
    secondary: 'text-gray-600',
    tertiary: 'text-gray-500',
    inverse: 'text-white'
  },
  border: {
    primary: 'border-gray-200',
    secondary: 'border-gray-300',
    focus: 'border-blue-500'
  },
  interactive: {
    primary: 'bg-blue-600 hover:bg-blue-700',
    primaryHover: 'hover:bg-blue-50',
    secondary: 'bg-gray-100 hover:bg-gray-200',
    secondaryHover: 'hover:bg-gray-50',
    danger: 'bg-red-600 hover:bg-red-700',
    dangerHover: 'hover:bg-red-50',
    success: 'bg-green-600 hover:bg-green-700',
    warning: 'bg-yellow-500 hover:bg-yellow-600'
  },
  status: {
    active: 'text-green-600',
    inactive: 'text-gray-500',
    pending: 'text-yellow-600',
    error: 'text-red-600'
  }
}

export const darkTheme: ThemeColors = {
  background: {
    primary: 'dark:bg-gray-900',
    secondary: 'dark:bg-gray-800',
    tertiary: 'dark:bg-gray-700',
    elevated: 'dark:bg-gray-800'
  },
  text: {
    primary: 'dark:text-white',
    secondary: 'dark:text-gray-300',
    tertiary: 'dark:text-gray-400',
    inverse: 'dark:text-gray-900'
  },
  border: {
    primary: 'dark:border-gray-700',
    secondary: 'dark:border-gray-600',
    focus: 'dark:border-blue-400'
  },
  interactive: {
    primary: 'dark:bg-blue-600 dark:hover:bg-blue-500',
    primaryHover: 'dark:hover:bg-gray-800',
    secondary: 'dark:bg-gray-700 dark:hover:bg-gray-600',
    secondaryHover: 'dark:hover:bg-gray-800',
    danger: 'dark:bg-red-600 dark:hover:bg-red-500',
    dangerHover: 'dark:hover:bg-red-900',
    success: 'dark:bg-green-600 dark:hover:bg-green-500',
    warning: 'dark:bg-yellow-500 dark:hover:bg-yellow-400'
  },
  status: {
    active: 'dark:text-green-400',
    inactive: 'dark:text-gray-500',
    pending: 'dark:text-yellow-400',
    error: 'dark:text-red-400'
  }
}

/**
 * Component-specific theme classes
 */
export const componentThemes = {
  // Card/Panel components
  card: {
    base: `${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.border.primary} ${darkTheme.border.primary}`,
    elevated: `${lightTheme.background.elevated} ${darkTheme.background.elevated} shadow-sm border rounded-xl`,
    header: `${lightTheme.text.primary} ${darkTheme.text.primary}`,
    content: `${lightTheme.text.secondary} ${darkTheme.text.secondary}`
  },
  
  // Form components
  form: {
    label: `${lightTheme.text.primary} ${darkTheme.text.primary} block text-sm font-medium mb-1`,
    input: `block w-full px-3 py-2 border ${lightTheme.border.secondary} ${darkTheme.border.secondary} ${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.text.primary} ${darkTheme.text.primary} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,
    select: `block w-full px-3 py-2 border ${lightTheme.border.secondary} ${darkTheme.border.secondary} ${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.text.primary} ${darkTheme.text.primary} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,
    helpText: `${lightTheme.text.tertiary} ${darkTheme.text.tertiary} text-xs mt-1`
  },
  
  // Button components
  button: {
    primary: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md ${lightTheme.text.inverse} ${lightTheme.interactive.primary} ${darkTheme.interactive.primary} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200`,
    secondary: `inline-flex items-center px-4 py-2 border ${lightTheme.border.secondary} ${darkTheme.border.secondary} shadow-sm text-sm font-medium rounded-md ${lightTheme.text.primary} ${darkTheme.text.primary} ${lightTheme.interactive.secondary} ${darkTheme.interactive.secondary} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200`,
    danger: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md ${lightTheme.text.inverse} ${lightTheme.interactive.danger} ${darkTheme.interactive.danger} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200`
  },
  
  // Navigation components
  navigation: {
    sidebar: `${lightTheme.background.secondary} ${darkTheme.background.secondary}`,
    sidebarItem: `flex items-center px-3 py-2 rounded-lg ${lightTheme.text.secondary} ${darkTheme.text.secondary} ${lightTheme.interactive.primaryHover} ${darkTheme.interactive.primaryHover} transition-all duration-200`,
    sidebarItemActive: `${lightTheme.interactive.primary} ${darkTheme.interactive.primary} ${lightTheme.text.inverse}`,
    topbar: `${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.border.primary} ${darkTheme.border.primary}`
  },
  
  // Table components
  table: {
    container: `${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.border.primary} ${darkTheme.border.primary} rounded-xl shadow-sm border`,
    header: `${lightTheme.background.secondary} ${darkTheme.background.secondary} ${lightTheme.text.primary} ${darkTheme.text.primary}`,
    row: `${lightTheme.background.primary} ${darkTheme.background.primary} ${lightTheme.interactive.primaryHover} ${darkTheme.interactive.primaryHover}`,
    cell: `${lightTheme.text.secondary} ${darkTheme.text.secondary}`
  }
}

/**
 * Utility function to get theme classes
 */
export function getThemeClasses(component: keyof typeof componentThemes, variant?: string): string {
  const componentTheme = componentThemes[component]
  if (typeof componentTheme === 'string') {
    return componentTheme
  }
  
  if (variant && typeof componentTheme === 'object' && variant in componentTheme) {
    return (componentTheme as any)[variant]
  }
  
  return (componentTheme as any).base || ''
}

/**
 * CSS Custom Properties for dynamic theming
 */
export const cssCustomProperties = {
  light: {
    '--color-bg-primary': '#ffffff',
    '--color-bg-secondary': '#f9fafb',
    '--color-bg-tertiary': '#f3f4f6',
    '--color-text-primary': '#111827',
    '--color-text-secondary': '#6b7280',
    '--color-text-tertiary': '#9ca3af',
    '--color-border-primary': '#e5e7eb',
    '--color-border-secondary': '#d1d5db',
    '--color-interactive-primary': '#2563eb',
    '--color-interactive-primary-hover': '#1d4ed8'
  },
  dark: {
    '--color-bg-primary': '#111827',
    '--color-bg-secondary': '#1f2937',
    '--color-bg-tertiary': '#374151',
    '--color-text-primary': '#ffffff',
    '--color-text-secondary': '#d1d5db',
    '--color-text-tertiary': '#9ca3af',
    '--color-border-primary': '#374151',
    '--color-border-secondary': '#4b5563',
    '--color-interactive-primary': '#3b82f6',
    '--color-interactive-primary-hover': '#60a5fa'
  }
}
