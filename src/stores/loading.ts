import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface LoadingState {
  isLoading: boolean
  message: string
  progress?: number
}

export const useLoadingStore = defineStore('loading', () => {
  // State
  const loadingStates = ref<Map<string, LoadingState>>(new Map())
  const defaultMessage = ref('Loading...')

  // Computed
  const isGlobalLoading = computed(() => {
    return Array.from(loadingStates.value.values()).some(state => state.isLoading)
  })

  const currentLoadingMessage = computed(() => {
    const activeStates = Array.from(loadingStates.value.values()).filter(state => state.isLoading)
    if (activeStates.length === 0) return defaultMessage.value
    
    // Return the most recent loading message
    return activeStates[activeStates.length - 1].message || defaultMessage.value
  })

  const loadingProgress = computed(() => {
    const activeStates = Array.from(loadingStates.value.values()).filter(state => state.isLoading)
    if (activeStates.length === 0) return undefined
    
    // Return progress if any active state has it
    const stateWithProgress = activeStates.find(state => state.progress !== undefined)
    return stateWithProgress?.progress
  })

  const activeLoadingCount = computed(() => {
    return Array.from(loadingStates.value.values()).filter(state => state.isLoading).length
  })

  // Actions
  const startLoading = (key: string = 'default', message: string = 'Loading...', progress?: number) => {
    loadingStates.value.set(key, {
      isLoading: true,
      message,
      progress
    })
  }

  const stopLoading = (key: string = 'default') => {
    const state = loadingStates.value.get(key)
    if (state) {
      state.isLoading = false
      // Remove the state after a short delay to allow for smooth transitions
      setTimeout(() => {
        loadingStates.value.delete(key)
      }, 300)
    }
  }

  const updateProgress = (key: string = 'default', progress: number, message?: string) => {
    const state = loadingStates.value.get(key)
    if (state && state.isLoading) {
      state.progress = progress
      if (message) {
        state.message = message
      }
    }
  }

  const updateMessage = (key: string = 'default', message: string) => {
    const state = loadingStates.value.get(key)
    if (state && state.isLoading) {
      state.message = message
    }
  }

  const stopAllLoading = () => {
    loadingStates.value.clear()
  }

  const isLoading = (key: string = 'default'): boolean => {
    const state = loadingStates.value.get(key)
    return state?.isLoading || false
  }

  // Utility methods for common API operations
  const withLoading = async <T>(
    operation: () => Promise<T>,
    key: string = 'default',
    message: string = 'Loading...'
  ): Promise<T> => {
    try {
      startLoading(key, message)
      const result = await operation()
      return result
    } finally {
      stopLoading(key)
    }
  }

  const withProgressLoading = async <T>(
    operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    key: string = 'default',
    initialMessage: string = 'Loading...'
  ): Promise<T> => {
    try {
      startLoading(key, initialMessage, 0)
      
      const progressUpdater = (progress: number, message?: string) => {
        updateProgress(key, progress, message)
      }
      
      const result = await operation(progressUpdater)
      return result
    } finally {
      stopLoading(key)
    }
  }

  return {
    // State
    loadingStates,
    defaultMessage,

    // Computed
    isGlobalLoading,
    currentLoadingMessage,
    loadingProgress,
    activeLoadingCount,

    // Actions
    startLoading,
    stopLoading,
    updateProgress,
    updateMessage,
    stopAllLoading,
    isLoading,

    // Utility methods
    withLoading,
    withProgressLoading
  }
})

// Composable for easy use in components
export function useGlobalLoading() {
  const loadingStore = useLoadingStore()

  return {
    // Direct access to store
    ...loadingStore,

    // Convenience methods
    showLoading: (message: string = 'Loading...', key: string = 'default') => {
      loadingStore.startLoading(key, message)
    },

    hideLoading: (key: string = 'default') => {
      loadingStore.stopLoading(key)
    },

    // For API calls
    loadingWrapper: async <T>(
      apiCall: () => Promise<T>,
      message: string = 'Loading...',
      key: string = 'api'
    ): Promise<T> => {
      return loadingStore.withLoading(apiCall, key, message)
    }
  }
}
