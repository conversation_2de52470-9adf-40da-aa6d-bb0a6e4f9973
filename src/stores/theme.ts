import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { cssCustomProperties } from '@/config/theme'

export type Theme = 'light' | 'dark' | 'system'

export const useThemeStore = defineStore('theme', () => {
  // State
  const theme = ref<Theme>('system')
  const systemPrefersDark = ref(false)

  // Computed
  const isDark = computed(() => {
    if (theme.value === 'system') {
      return systemPrefersDark.value
    }
    return theme.value === 'dark'
  })

  const currentTheme = computed(() => {
    return isDark.value ? 'dark' : 'light'
  })

  // Actions
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    applyTheme()
  }

  const toggleTheme = () => {
    if (theme.value === 'light') {
      setTheme('dark')
    } else if (theme.value === 'dark') {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  const applyTheme = () => {
    const root = document.documentElement

    if (isDark.value) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }

    // Apply CSS custom properties
    const properties = isDark.value ? cssCustomProperties.dark : cssCustomProperties.light
    Object.entries(properties).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
  }

  const initializeTheme = () => {
    // Check system preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemPrefersDark.value = mediaQuery.matches

    // Listen for system preference changes
    mediaQuery.addEventListener('change', (e) => {
      systemPrefersDark.value = e.matches
      if (theme.value === 'system') {
        applyTheme()
      }
    })

    // Apply initial theme
    applyTheme()
  }

  // Watch for theme changes
  watch(isDark, () => {
    applyTheme()
  })

  return {
    // State
    theme,
    systemPrefersDark,
    
    // Computed
    isDark,
    currentTheme,
    
    // Actions
    setTheme,
    toggleTheme,
    initializeTheme
  }
}, {
  persist: {
    key: 'theme-store',
    storage: localStorage,
    paths: ['theme']
  }
})
