import Swal from 'sweetalert2'
import type { SweetAlertOptions, SweetAlertResult } from 'sweetalert2'

/**
 * SweetAlert2 composable with theme-aware styling
 * Provides consistent alert dialogs across the application
 */
export function useSweetAlert() {
  // Get current theme for styling
  const isDark = document.documentElement.classList.contains('dark')

  // Base configuration for theme consistency
  const baseConfig: SweetAlertOptions = {
    customClass: {
      popup: isDark 
        ? 'bg-gray-800 border border-gray-700' 
        : 'bg-white border border-gray-200',
      title: isDark ? 'text-white' : 'text-gray-900',
      htmlContainer: isDark ? 'text-gray-300' : 'text-gray-600',
      confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200',
      cancelButton: 'bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200 mr-2',
      denyButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200'
    },
    buttonsStyling: false,
    reverseButtons: true
  }

  /**
   * Show success message
   */
  const showSuccess = (
    title: string = 'Success!',
    text?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      icon: 'success',
      title,
      text,
      confirmButtonText: 'OK',
      timer: 3000,
      timerProgressBar: true,
      ...options
    })
  }

  /**
   * Show error message
   */
  const showError = (
    title: string = 'Error!',
    text?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      icon: 'error',
      title,
      text,
      confirmButtonText: 'OK',
      ...options
    })
  }

  /**
   * Show warning message
   */
  const showWarning = (
    title: string = 'Warning!',
    text?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      icon: 'warning',
      title,
      text,
      confirmButtonText: 'OK',
      ...options
    })
  }

  /**
   * Show info message
   */
  const showInfo = (
    title: string = 'Information',
    text?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      icon: 'info',
      title,
      text,
      confirmButtonText: 'OK',
      ...options
    })
  }

  /**
   * Show confirmation dialog
   */
  const showConfirmation = (
    title: string = 'Are you sure?',
    text?: string,
    confirmText: string = 'Yes',
    cancelText: string = 'Cancel',
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      icon: 'question',
      title,
      text,
      showCancelButton: true,
      confirmButtonText: confirmText,
      cancelButtonText: cancelText,
      ...options
    })
  }

  /**
   * Show delete confirmation
   */
  const showDeleteConfirmation = (
    itemName?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    const title = itemName ? `Delete ${itemName}?` : 'Delete this item?'
    const text = 'This action cannot be undone!'
    
    return Swal.fire({
      ...baseConfig,
      icon: 'warning',
      title,
      text,
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        ...baseConfig.customClass,
        confirmButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200'
      },
      ...options
    })
  }

  /**
   * Show input dialog
   */
  const showInput = (
    title: string,
    inputPlaceholder?: string,
    inputValue?: string,
    options?: SweetAlertOptions
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      ...baseConfig,
      title,
      input: 'text',
      inputPlaceholder,
      inputValue,
      showCancelButton: true,
      confirmButtonText: 'Submit',
      cancelButtonText: 'Cancel',
      inputValidator: (value) => {
        if (!value) {
          return 'You need to write something!'
        }
      },
      ...options
    })
  }

  /**
   * Show loading dialog
   */
  const showLoading = (
    title: string = 'Loading...',
    text?: string
  ): void => {
    Swal.fire({
      ...baseConfig,
      title,
      text,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading()
      }
    })
  }

  /**
   * Close any open dialog
   */
  const close = (): void => {
    Swal.close()
  }

  /**
   * Show toast notification
   */
  const showToast = (
    icon: 'success' | 'error' | 'warning' | 'info',
    title: string,
    position: 'top' | 'top-start' | 'top-end' | 'center' | 'center-start' | 'center-end' | 'bottom' | 'bottom-start' | 'bottom-end' = 'top-end',
    timer: number = 3000
  ): Promise<SweetAlertResult> => {
    return Swal.fire({
      icon,
      title,
      toast: true,
      position,
      showConfirmButton: false,
      timer,
      timerProgressBar: true,
      customClass: {
        popup: isDark 
          ? 'bg-gray-800 border border-gray-700 text-white' 
          : 'bg-white border border-gray-200 text-gray-900'
      }
    })
  }

  /**
   * Utility methods for common operations
   */
  const confirmDelete = async (itemName?: string): Promise<boolean> => {
    const result = await showDeleteConfirmation(itemName)
    return result.isConfirmed
  }

  const confirmAction = async (
    title: string,
    text?: string,
    confirmText: string = 'Yes'
  ): Promise<boolean> => {
    const result = await showConfirmation(title, text, confirmText)
    return result.isConfirmed
  }

  const handleApiResponse = (
    response: any,
    successMessage: string = 'Operation completed successfully!',
    errorMessage: string = 'An error occurred!'
  ): void => {
    if (response.status === 200 || response.success) {
      showToast('success', successMessage)
    } else {
      showToast('error', response.message || errorMessage)
    }
  }

  return {
    // Basic alerts
    showSuccess,
    showError,
    showWarning,
    showInfo,
    
    // Confirmation dialogs
    showConfirmation,
    showDeleteConfirmation,
    
    // Input dialog
    showInput,
    
    // Loading
    showLoading,
    close,
    
    // Toast notifications
    showToast,
    
    // Utility methods
    confirmDelete,
    confirmAction,
    handleApiResponse,
    
    // Direct access to Swal for custom use
    Swal
  }
}
