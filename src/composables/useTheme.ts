import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { getThemeClasses, componentThemes, cssCustomProperties } from '@/config/theme'

/**
 * Theme utility composable
 * Provides easy access to theme classes and utilities
 */
export function useTheme() {
  const themeStore = useThemeStore()

  /**
   * Get theme classes for a specific component
   */
  const getComponentClasses = (component: keyof typeof componentThemes, variant?: string): string => {
    return getThemeClasses(component, variant)
  }

  /**
   * Apply CSS custom properties to document root
   */
  const applyCSSCustomProperties = () => {
    const root = document.documentElement
    const properties = themeStore.isDark ? cssCustomProperties.dark : cssCustomProperties.light
    
    Object.entries(properties).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
  }

  /**
   * Common theme class combinations
   */
  const themeClasses = computed(() => ({
    // Page layouts
    page: `min-h-screen bg-gray-50 dark:bg-gray-900`,
    pageContent: `bg-white dark:bg-gray-900 text-gray-900 dark:text-white`,
    
    // Cards and panels
    card: `bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm`,
    cardHeader: `text-gray-900 dark:text-white`,
    cardContent: `text-gray-600 dark:text-gray-300`,
    
    // Forms
    formLabel: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1`,
    formInput: `block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,
    formSelect: `block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,
    formHelpText: `text-xs text-gray-500 dark:text-gray-400 mt-1`,
    
    // Buttons
    buttonPrimary: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200`,
    buttonSecondary: `inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200`,
    buttonDanger: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200`,
    
    // Navigation
    sidebarItem: `flex items-center px-3 py-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200`,
    sidebarItemActive: `bg-blue-600 text-white`,
    
    // Tables
    tableContainer: `bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm`,
    tableHeader: `bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white`,
    tableRow: `bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700`,
    tableCell: `text-gray-600 dark:text-gray-300`,
    
    // Status indicators
    statusActive: `text-green-600 dark:text-green-400`,
    statusInactive: `text-gray-500 dark:text-gray-400`,
    statusPending: `text-yellow-600 dark:text-yellow-400`,
    statusError: `text-red-600 dark:text-red-400`,
    
    // Loading states
    loadingSpinner: `animate-spin rounded-full border-b-2 border-blue-600 dark:border-blue-400`,
    loadingText: `text-gray-600 dark:text-gray-400`,
    
    // Dropdowns and modals
    dropdown: `bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg`,
    dropdownItem: `text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700`,
    modal: `bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl`,
    modalOverlay: `bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75`,
    
    // Text styles
    textPrimary: `text-gray-900 dark:text-white`,
    textSecondary: `text-gray-600 dark:text-gray-300`,
    textTertiary: `text-gray-500 dark:text-gray-400`,
    textMuted: `text-gray-400 dark:text-gray-500`,
    
    // Background styles
    bgPrimary: `bg-white dark:bg-gray-900`,
    bgSecondary: `bg-gray-50 dark:bg-gray-800`,
    bgTertiary: `bg-gray-100 dark:bg-gray-700`,
    
    // Border styles
    borderPrimary: `border-gray-200 dark:border-gray-700`,
    borderSecondary: `border-gray-300 dark:border-gray-600`,
    borderFocus: `border-blue-500 dark:border-blue-400`
  }))

  /**
   * Get status color class
   */
  const getStatusClass = (status: string | number): string => {
    const statusStr = status.toString().toLowerCase()
    
    if (statusStr === '1' || statusStr === 'active' || statusStr === 'enabled') {
      return themeClasses.value.statusActive
    }
    if (statusStr === '0' || statusStr === 'inactive' || statusStr === 'disabled') {
      return themeClasses.value.statusInactive
    }
    if (statusStr === 'pending' || statusStr === 'processing') {
      return themeClasses.value.statusPending
    }
    if (statusStr === 'error' || statusStr === 'failed') {
      return themeClasses.value.statusError
    }
    
    return themeClasses.value.textSecondary
  }

  /**
   * Get button variant classes
   */
  const getButtonClass = (variant: 'primary' | 'secondary' | 'danger' = 'primary'): string => {
    switch (variant) {
      case 'primary':
        return themeClasses.value.buttonPrimary
      case 'secondary':
        return themeClasses.value.buttonSecondary
      case 'danger':
        return themeClasses.value.buttonDanger
      default:
        return themeClasses.value.buttonPrimary
    }
  }

  return {
    // Store access
    themeStore,
    isDark: computed(() => themeStore.isDark),
    currentTheme: computed(() => themeStore.currentTheme),
    
    // Theme classes
    themeClasses,
    
    // Utility functions
    getComponentClasses,
    applyCSSCustomProperties,
    getStatusClass,
    getButtonClass
  }
}
