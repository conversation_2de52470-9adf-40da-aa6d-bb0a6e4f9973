# SPA Routing Fix for 404 Errors on Page Refresh

## Problem
When users refresh the page or directly access a URL in a Single Page Application (SPA), they get a 404 error because the server doesn't know how to handle client-side routes.

## Solution
Configure your web server to serve the `index.html` file for all routes that don't correspond to actual files.

## Server Configurations

### 1. Nginx (Recommended)
Use the provided `nginx.conf` file. Key configuration:

```nginx
location / {
    try_files $uri $uri/ /index.html;
}

error_page 404 /index.html;
```

### 2. Apache
Use the provided `.htaccess` file. Key configuration:

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

ErrorDocument 404 /index.html
```

### 3. Node.js/Express
If using Express server:

```javascript
const express = require('express');
const path = require('path');
const app = express();

// Serve static files
app.use(express.static(path.join(__dirname, 'dist')));

// Handle SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/index.html'));
});

app.listen(3000);
```

### 4. Netlify
Create a `_redirects` file in the `public` folder:

```
/*    /index.html   200
```

### 5. Vercel
Create a `vercel.json` file:

```json
{
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

## Deployment Checklist

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Upload the `dist` folder** to your web server

3. **Configure your web server** using one of the configurations above

4. **Test the deployment**:
   - Visit the main URL
   - Navigate to different pages
   - Refresh the page on different routes
   - Test direct URL access

## Common Issues and Solutions

### Issue: Still getting 404 errors
**Solutions**:
- Ensure the server configuration file is in the correct location
- Check that the web server is actually using the configuration
- Verify that the `index.html` file exists in the root directory
- Check server logs for specific error messages

### Issue: Assets not loading
**Solutions**:
- Ensure the `base` URL in Vite config matches your deployment path
- Check that asset paths are relative, not absolute
- Verify that static files are being served correctly

### Issue: API calls failing
**Solutions**:
- Update API base URL for production environment
- Check CORS configuration on your API server
- Ensure API endpoints are accessible from the production domain

## Environment-Specific Configuration

### Development
- Uses Vite dev server with built-in SPA support
- Proxy configuration handles API calls

### Production
- Requires web server configuration for SPA routing
- Static files served directly by web server
- API calls go directly to production API

## Testing Your Deployment

1. **Direct URL Test**: Try accessing `https://yourdomain.com/partners/bets` directly
2. **Refresh Test**: Navigate to a page and refresh the browser
3. **Deep Link Test**: Share a deep link and open it in a new browser
4. **Mobile Test**: Test on mobile devices and different browsers

## Troubleshooting Commands

### Check Nginx Configuration
```bash
nginx -t
sudo systemctl reload nginx
```

### Check Apache Configuration
```bash
apache2ctl configtest
sudo systemctl reload apache2
```

### View Server Logs
```bash
# Nginx
sudo tail -f /var/log/nginx/error.log

# Apache
sudo tail -f /var/log/apache2/error.log
```

## Additional Notes

- The provided configurations include security headers and caching optimizations
- Static assets are cached for 1 year for better performance
- HTML files are not cached to ensure updates are immediately available
- GZIP compression is enabled for better performance
