# Mossbets B2B Dashboard - Comprehensive Documentation

## Table of Contents
1. [System Architecture](#system-architecture)
2. [Authentication Improvements](#authentication-improvements)
3. [Deployment Guide](#deployment-guide)
4. [Form Prefill Implementation](#form-prefill-implementation)
5. [Implementation Summary](#implementation-summary)
6. [SPA Routing Fix](#spa-routing-fix)
7. [API Configuration](#api-configuration)
8. [Development Guidelines](#development-guidelines)

---

## System Architecture

### Overview
The Mossbets B2B Dashboard is a Vue.js 3 single-page application (SPA) built with TypeScript, Tailwind CSS, and Pinia for state management. It provides a comprehensive interface for managing partners, users, bets, and system settings.

### Technology Stack
- **Frontend**: Vue.js 3 with Composition API
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Build Tool**: Vite
- **HTTP Client**: Axios
- **UI Components**: Headless UI, Heroicons
- **Charts**: Chart.js
- **Notifications**: SweetAlert2

### Architecture Diagram
```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Vue.js SPA)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Views     │  │ Components  │  │   Stores    │         │
│  │             │  │             │  │   (Pinia)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Router    │  │   Services  │  │   Utils     │         │
│  │ (Vue Router)│  │   (API)     │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTPS/API Calls
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Backend API Server                       │
│                (b2b.mb.mb-gaming.life)                     │
└─────────────────────────────────────────────────────────────┘
```

### Key Features
- **Multi-tenant Partner Management**: Support for multiple partners with role-based access
- **User Management**: Comprehensive user creation, editing, and permission management
- **Betting System**: Real-time bet tracking, slip management, and reporting
- **Dashboard Analytics**: Interactive charts and statistics
- **Responsive Design**: Mobile-first approach with dark mode support
- **Real-time Updates**: Live data updates and notifications
- **Security**: JWT authentication, role-based permissions, CORS handling

### Directory Structure
```
src/
├── assets/          # Static assets (CSS, images)
├── components/      # Reusable Vue components
│   ├── Forms/       # Form-specific components
│   └── UI/          # Generic UI components
├── composables/     # Vue composition functions
├── config/          # Configuration files
├── layouts/         # Layout components
├── plugins/         # Vue plugins
├── router/          # Vue Router configuration
├── services/        # API service layer
├── stores/          # Pinia stores
├── utils/           # Utility functions
└── views/           # Page components
    ├── Auth/        # Authentication pages
    ├── Dashboard/   # Dashboard pages
    ├── Partners/    # Partner management
    ├── System/      # System administration
    └── Users/       # User management
```

---

## Authentication Improvements

### Overview
Enhanced authentication system with improved error handling, multi-partner support, and role-based access control.

### Key Features

#### 1. Multi-Partner Authentication
- Support for users with multiple partner assignments
- Partner selection modal after successful login
- Global partner context management
- Partner-specific data filtering

#### 2. Enhanced Error Handling
- SweetAlert integration for user-friendly error messages
- Specific handling for authentication/authorization errors
- Graceful degradation for network issues
- Detailed error logging for debugging

#### 3. Role-Based Access Control
- Admin, Partner, and Partner Admin roles
- Permission-based UI component visibility
- Route-level access control
- Dynamic menu generation based on permissions

#### 4. Session Management
- Automatic token refresh
- Secure token storage
- Session timeout handling
- Remember me functionality

### Implementation Details

#### Authentication Store (Pinia)
```typescript
// Key computed properties
const isAuthenticated = computed(() => !!token.value)
const isAdmin = computed(() => user.value?.user_type === 'Admin')
const isPartner = computed(() => user.value?.user_type === 'Partner')
const isPartnerAdmin = computed(() => role.value === 5)
```

#### API Integration
- JWT token-based authentication
- Automatic header injection for authenticated requests
- CORS handling with proper headers
- Error interceptors for token expiration

#### Security Features
- XSS protection through proper data sanitization
- CSRF protection via token validation
- Secure cookie handling
- Content Security Policy headers

---

## Deployment Guide

### Prerequisites
- Node.js 18+ and npm
- Web server (Nginx recommended)
- SSL certificate for HTTPS
- Domain name configured

### Build Process
```bash
# Install dependencies
npm install

# Build for production
npm run build

# The built files will be in the 'dist' directory
```

### Server Configuration

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    # Handle SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy (if needed)
    location /api/ {
        proxy_pass https://b2b.mb.mb-gaming.life/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

#### Environment Configuration
```bash
# Production environment variables
VITE_API_ENVIRONMENT=production
VITE_API_BASE_URL=https://b2b.mb.mb-gaming.life
```

### Deployment Steps
1. Build the application: `npm run build`
2. Upload `dist` folder contents to web server
3. Configure web server with provided configuration
4. Set up SSL certificate
5. Configure DNS records
6. Test deployment thoroughly

### Performance Optimization
- Gzip compression enabled
- Static asset caching (1 year)
- Code splitting for vendor libraries
- Lazy loading for routes
- Image optimization

---

## Form Prefill Implementation

### Overview
Intelligent form prefilling system that improves user experience by automatically populating forms with relevant data from navigation context.

### Key Features

#### 1. Navigation Cache System
```typescript
// Store data when navigating
navigateWithCache(router, data, 'key', 'target-route')

// Retrieve data in target component
const cachedData = getNavigationData('key')

// Clear data when no longer needed
clearNavigationData('key')
```

#### 2. Context-Aware Prefilling
- Partner details prefill user forms
- Bet data prefills bet slip forms
- User data prefills edit forms
- Role-based field population

#### 3. Smart Data Mapping
- Automatic field mapping based on data structure
- Type-safe data transformation
- Validation-aware prefilling
- Conditional field population

### Implementation Examples

#### User Form Prefilling
```typescript
// When navigating from partner details to add user
const prefillUserForm = (partnerData: any) => {
  if (partnerData) {
    userForm.value.partner_ids = [partnerData.id]
    userForm.value.user_type = 'Partner'
    // Additional prefilling logic
  }
}
```

#### Role Template System
```typescript
// Automatic permission selection based on role
const onRoleSelected = (role: any) => {
  if (role?.permissions_acl) {
    const permissions = parsePermissionsAcl(role.permissions_acl)
    selectedPermissions.value = permissions
  }
}
```

---

## Implementation Summary

### Recent Enhancements

#### 1. Navigation and Caching
- ✅ Bet-to-BetSlips navigation pattern
- ✅ Conditional UI based on navigation source
- ✅ Automatic cache clearing on navigation away
- ✅ Partner context preservation

#### 2. Multi-Partner Selection
- ✅ Admin multi-partner selection in topbar
- ✅ Checkbox-based partner selection
- ✅ "All Partners" functionality
- ✅ Selected partners display

#### 3. User Management Improvements
- ✅ Fixed partner search affecting user edit
- ✅ Role permissions preselection
- ✅ Partner admin role support (ID 5)
- ✅ User edit actions in partner details

#### 4. UI/UX Enhancements
- ✅ Fixed sidebar navigation issues
- ✅ Dark mode support for all elements
- ✅ Mobile-responsive action buttons
- ✅ Improved error handling with showErr=false

#### 5. Table and Search Improvements
- ✅ Flexible table search (local by default)
- ✅ searchApi flag for API search
- ✅ Mobile button wrapping
- ✅ Enhanced action button styling

#### 6. New Pages and Routes
- ✅ Add Partner Service page
- ✅ Add Partner Setting page
- ✅ Proper routing and navigation
- ✅ Context-aware form prefilling

### Technical Improvements
- Enhanced error handling with SweetAlert
- Improved API response parsing
- Better TypeScript type safety
- Optimized component performance
- Comprehensive testing coverage

---

## SPA Routing Fix

### Problem
404 errors when refreshing pages or accessing direct URLs in production.

### Solution
Configure web server to serve index.html for all routes.

### Server Configurations Provided
- Nginx configuration (nginx.conf)
- Apache configuration (.htaccess)
- Node.js/Express setup
- Netlify and Vercel configurations

### Key Configuration
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
error_page 404 /index.html;
```

---

## API Configuration

### Environment Management
```typescript
const API_ENVIRONMENTS = {
  development: 'https://b2b.mb.mb-gaming.life',
  production: 'https://b2b.mb.mb-gaming.life'
}
```

### CORS Handling
- Proper header configuration
- Preflight request handling
- Credential support
- Error handling for CORS issues

### Request/Response Interceptors
- Automatic token injection
- Error response handling
- Request/response logging
- Retry logic for failed requests

---

## Development Guidelines

### Code Standards
- TypeScript strict mode enabled
- ESLint and Prettier configuration
- Vue 3 Composition API preferred
- Reactive programming patterns

### Component Guidelines
- Single responsibility principle
- Props validation with TypeScript
- Emit events for parent communication
- Slot usage for flexible content

### State Management
- Pinia stores for global state
- Reactive refs for local state
- Computed properties for derived state
- Actions for async operations

### Testing Strategy
- Unit tests for utilities and composables
- Component testing with Vue Test Utils
- E2E testing for critical user flows
- API mocking for development

### Performance Best Practices
- Lazy loading for routes
- Component lazy loading
- Image optimization
- Bundle size monitoring
- Memory leak prevention

---

## Conclusion

This comprehensive documentation covers all aspects of the Mossbets B2B Dashboard implementation, from system architecture to deployment and maintenance. The application provides a robust, scalable, and user-friendly interface for managing betting operations with enterprise-grade security and performance.
